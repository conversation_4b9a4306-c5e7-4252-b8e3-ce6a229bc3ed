(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5779],{933:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h});var a=r(5155),o=r(2115),s=r(6268),i=r(1032),n=r(1725),l=r(8543);function c(e){let{params:t}=e,[r,c]=(0,o.useState)([]),[d,h]=(0,o.useState)([]),u=t.id;(0,o.useEffect)(()=>{(async()=>{try{let e=localStorage.getItem("sessionToken")||"",t=await n.A.fetchLogs(u,e);t&&t.payload.userLogs?(c(t.payload.userLogs),h(t.payload.user)):l.oR.error("No logs found for this user.")}catch(e){l.oR.error("An error occurred while fetching logs. Please try again.")}})()},[u]);let g=[{accessorKey:"ip",header:"IP"},{accessorKey:"device",header:"Thiết bị"},{accessorKey:"location",header:"Vị tr\xed"},{accessorKey:"loginTime",header:"Đăng nhập",cell:e=>{let{row:t}=e;return t.original.loginTime?new Date(t.original.loginTime).toLocaleString("vi-VN",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}):"N/A"}},{accessorKey:"logoutTime",header:"Đăng xuất",cell:e=>{let{row:t}=e;return t.original.logoutTime?new Date(t.original.logoutTime).toLocaleString("vi-VN",{day:"2-digit",month:"2-digit",year:"numeric",hour:"2-digit",minute:"2-digit",second:"2-digit",hour12:!1}):"Chưa đăng xuất"}}],p=(0,s.N4)({data:r,columns:g,getCoreRowModel:(0,i.HT)()});return(0,a.jsxs)("div",{className:"w-full",children:[(0,a.jsxs)("p",{className:"font-bold mb-4 text-gray-800",children:["Th\xe0nh Vi\xean: ",d.username]}),(0,a.jsxs)("table",{className:"table-auto w-full border-collapse border border-gray-300 bg-white",children:[(0,a.jsx)("thead",{children:p.getHeaderGroups().map(e=>(0,a.jsx)("tr",{className:"bg-gray-200",children:e.headers.map(e=>(0,a.jsx)("th",{className:"border border-gray-300 px-4 py-2 text-gray-800 font-semibold",children:(0,s.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,a.jsx)("tbody",{children:p.getRowModel().rows.length>0?p.getRowModel().rows.map(e=>(0,a.jsx)("tr",{className:"border border-gray-300 hover:bg-gray-50",children:e.getVisibleCells().map(e=>(0,a.jsx)("td",{className:"border border-gray-300 px-4 py-2 text-gray-900",children:(0,s.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id)):(0,a.jsx)("tr",{children:(0,a.jsx)("td",{colSpan:g.length,className:"text-center p-4 text-gray-600",children:"Kh\xf4ng c\xf3 lịch sử đăng nhập"})})})]})]})}var d=r(5695);function h(){let e=(0,d.useParams)();return(0,a.jsxs)("div",{className:"content",children:[(0,a.jsx)("h1",{className:"text-2xl mb-4",children:"Lịch Sử Log"}),(0,a.jsx)(c,{params:{id:e.id}})," "]})}},1725:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var a=r(7937);let o={fetchUsers:(e,t)=>a.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(t)}}),fetchLogs:(e,t)=>a.Ay.get("api/administrator/log/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),deleteUser:(e,t)=>a.Ay.delete("api/administrator/users/".concat(e._id),{headers:{Authorization:"Bearer ".concat(t)}}),fetchUserById:(e,t,r)=>a.Ay.get("api/administrator/users/".concat(e),{headers:{Authorization:"Bearer ".concat(t)},signal:r}),CreateUser:(e,t)=>a.Ay.post("api/administrator/signup",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateUser:(e,t)=>a.Ay.put("api/administrator/change-info/",e,{headers:{Authorization:"Bearer ".concat(t)}}),updatePassUser:(e,t)=>a.Ay.put("api/administrator/users/change-pass/",e,{headers:{Authorization:"Bearer ".concat(t)}})}},2721:(e,t,r)=>{Promise.resolve().then(r.bind(r,933))},4559:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var a=r(4556),o=r(9509);let s=a.Ik({NEXT_PUBLIC_API_ENDPOINT:a.Yj().url(),NEXT_PUBLIC_URL:a.Yj().url(),CRYPTOJS_SECRECT:a.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:o.env.CRYPTOJS_SECRECT});if(!s.success)throw console.error("Invalid environment variables:",s.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let i=s.data},7937:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d});var a=r(4559),o=r(9434),s=r(5695);class i extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class n extends i{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let l=null,c=async(e,t,r)=>{let c;(null==r?void 0:r.body)instanceof FormData?c=r.body:(null==r?void 0:r.body)&&(c=JSON.stringify(r.body));let d=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(d.Authorization="Bearer ".concat(e))}let h=(null==r?void 0:r.baseUrl)===void 0?a.A.NEXT_PUBLIC_API_ENDPOINT:r.baseUrl,u=t.startsWith("/")?"".concat(h).concat(t):"".concat(h,"/").concat(t),g=await fetch(u,{...r,headers:{...d,...null==r?void 0:r.headers},body:c,method:e}),p=null,m=g.headers.get("content-type");if(m&&m.includes("application/json"))try{p=await g.json()}catch(e){console.error("Failed to parse JSON response:",e),p=null}else p=await g.text();let y={status:g.status,payload:p};if(!g.ok)if(404===g.status||403===g.status)throw new n(y);else if(401===g.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,s.redirect)("/logout?sessionToken=".concat(e))}else if(!l){l=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...d}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await l}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),l=null,location.href="/login"}}}else throw new i(y);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,o.Fd)(t))){let{token:e}=p;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,o.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return y},d={get:(e,t)=>c("GET",e,t),post:(e,t,r)=>c("POST",e,{...r,body:t}),put:(e,t,r)=>c("PUT",e,{...r,body:t}),patch:(e,t,r)=>c("PATCH",e,{...r,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},9434:(e,t,r)=>{"use strict";r.d(t,{Fd:()=>i,cn:()=>s}),r(7937);var a=r(2596),o=r(9688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,a.$)(t))}r(8801);let i=e=>e.startsWith("/")?e.slice(1):e}},e=>{e.O(0,[9268,3235,8543,6268,8441,5964,7358],()=>e(e.s=2721)),_N_E=e.O()}]);