"use client";

import { useState } from "react";
import { CourtCaseItemType } from "@/schemaValidations/courtCase.schema";
import { formatDate } from "@/utils/formatters";
import { usePermissions } from "@/hooks/usePermissions";
import { toast } from "react-toastify";

interface CourtCaseListProps {
  cases: CourtCaseItemType[];
  onCaseSelect: (courtCase: CourtCaseItemType) => void;
  onCaseEdit: (courtCase: CourtCaseItemType) => void;
  onCaseDelete: (caseId: string) => void;
  onBulkAction: (caseIds: string[], action: "delete") => void;
  onSort?: (sortBy: string, sortOrder: 'asc' | 'desc') => void;
  currentSort?: { sortBy: string; sortOrder: 'asc' | 'desc' };
  loading?: boolean;
}

const CourtCaseList: React.FC<CourtCaseListProps> = ({
  cases,
  onCaseSelect,
  onCaseEdit,
  onCaseDelete,
  onBulkAction,
  onSort,
  currentSort,
  loading = false
}) => {
  const { hasPermission } = usePermissions();
  const [selectedCases, setSelectedCases] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  const handleSelectAll = (checked: boolean) => {
    setSelectAll(checked);
    if (checked) {
      setSelectedCases(cases.map(c => c._id));
    } else {
      setSelectedCases([]);
    }
  };

  const handleSelectCase = (caseId: string, checked: boolean) => {
    if (checked) {
      setSelectedCases(prev => [...prev, caseId]);
    } else {
      setSelectedCases(prev => prev.filter(id => id !== caseId));
      setSelectAll(false);
    }
  };

  const handleBulkDelete = () => {
    if (selectedCases.length === 0) {
      toast.warning("Vui lòng chọn ít nhất một vụ việc để xóa");
      return;
    }

    if (confirm(`Bạn có chắc chắn muốn xóa ${selectedCases.length} vụ việc đã chọn?`)) {
      onBulkAction(selectedCases, "delete");
      setSelectedCases([]);
      setSelectAll(false);
    }
  };

  const getStatusBadgeClass = (status: string) => {
    switch (status) {
      case 'Đã giải quyết':
        return 'bg-green-100 text-green-800';
      case 'Đang giải quyết':
        return 'bg-yellow-100 text-yellow-800';
      case 'Chưa giải quyết':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getDeadlineStatusClass = (status: string) => {
    switch (status) {
      case 'Quá hạn':
        return 'bg-red-100 text-red-800 border border-red-300';
      case 'Gần hết hạn': // 30-0 ngày màu đỏ
        return 'bg-red-100 text-red-800 border border-red-300';
      case 'Sắp hết hạn': // 60-31 ngày vàng
        return 'bg-yellow-100 text-yellow-800 border border-yellow-300';
      case 'Còn thời gian': // 90-61 ngày màu xanh
        return 'bg-green-100 text-green-800 border border-green-300';
      default:
        return 'bg-gray-100 text-gray-800 border border-gray-300';
    }
  };

  const formatRemainingDays = (days: number | undefined, status: string | undefined) => {
    if (days === undefined || status === undefined) return '';
    
    if (days < 0) {
      return `Quá hạn ${Math.abs(days)} ngày`;
    } else if (days === 0) {
      return 'Hết hạn hôm nay';
    } else {
      return `Còn ${days} ngày`;
    }
  };

  const handleSort = (sortBy: string) => {
    if (!onSort) return;
    
    let newSortOrder: 'asc' | 'desc' = 'asc';
    
    // If clicking the same column, toggle the order
    if (currentSort?.sortBy === sortBy) {
      newSortOrder = currentSort.sortOrder === 'asc' ? 'desc' : 'asc';
    }
    
    onSort(sortBy, newSortOrder);
  };

  const SortButton: React.FC<{ field: string; children: React.ReactNode }> = ({ field, children }) => {
    const isActive = currentSort?.sortBy === field;
    const isAsc = isActive && currentSort?.sortOrder === 'asc';
    const isDesc = isActive && currentSort?.sortOrder === 'desc';

    return (
      <button
        onClick={() => handleSort(field)}
        className="flex items-center gap-1 hover:text-gray-700 transition-colors"
      >
        {children}
        <span className="flex flex-col">
          <span className={`text-xs leading-none ${isAsc ? 'text-blue-600' : 'text-gray-400'}`}>▲</span>
          <span className={`text-xs leading-none ${isDesc ? 'text-blue-600' : 'text-gray-400'}`}>▼</span>
        </span>
      </button>
    );
  };

  const getTypeIcon = (type: string | undefined) => {
    if (!type) return '📄';

    // Check for common case types
    const lowerType = type.toLowerCase();
    if (lowerType.includes('hình sự')) return '⚖️';
    if (lowerType.includes('dân sự')) return '🏠';
    if (lowerType.includes('hành chính')) return '🏛️';
    if (lowerType.includes('kinh tế')) return '💼';
    if (lowerType.includes('lao động')) return '👷';

    // Default icon for any other type
    return '📋';
  };

  if (loading) {
    return (
      <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
        {/* Header Skeleton */}
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-4 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <div className="w-6 h-6 bg-gray-200 rounded animate-pulse"></div>
              </div>
              <div>
                <div className="h-5 w-48 bg-gray-200 rounded animate-pulse mb-2"></div>
                <div className="h-4 w-32 bg-gray-200 rounded animate-pulse"></div>
              </div>
            </div>
          </div>
        </div>
        
        {/* Table Skeleton */}
        <div className="p-6">
          <div className="space-y-4">
            {[...Array(8)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4 p-4 border border-gray-200 rounded-lg animate-pulse">
                <div className="w-8 h-8 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
                <div className="flex space-x-2">
                  <div className="w-8 h-8 bg-gray-200 rounded"></div>
                  <div className="w-8 h-8 bg-gray-200 rounded"></div>
                  <div className="w-8 h-8 bg-gray-200 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-xl shadow-lg overflow-hidden border border-gray-100">
      {/* Header with Stats */}
      <div className="bg-gray-50 px-6 py-4 border-b border-gray-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Danh sách vụ việc tòa án
            </h3>
            <p className="text-sm text-gray-600">
              Tổng số: <span className="font-medium text-blue-600">{cases.length}</span> vụ việc
            </p>
          </div>
          
          {/* Search and filters info */}
          <div className="hidden md:flex items-center gap-2 text-sm text-gray-500">
            <span className="px-2 py-1 bg-white rounded-md border">
              {cases.length} kết quả
            </span>
          </div>
        </div>
      </div>

      {/* Bulk Actions */}
      {selectedCases.length > 0 && hasPermission('court_case_delete') && (
        <div className="bg-blue-50 px-6 py-3 border-b border-blue-200">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <span className="text-sm text-blue-700 font-medium">
                Đã chọn {selectedCases.length} vụ việc
              </span>
            </div>
            <div className="flex gap-2">
              <button
                onClick={handleBulkDelete}
                className="flex items-center gap-1 px-3 py-1.5 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors font-medium"
              >
                Xóa đã chọn
              </button>
              <button
                onClick={() => {
                  setSelectedCases([]);
                  setSelectAll(false);
                }}
                className="flex items-center gap-1 px-3 py-1.5 bg-gray-500 text-white text-sm rounded-lg hover:bg-gray-600 transition-colors"
              >
                Bỏ chọn
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200" style={{ minWidth: '3000px' }}>
          <thead className="bg-gray-50">
            <tr>
              {hasPermission('court_case_delete') && (
                <th className="px-4 py-4 text-left w-12">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={(e) => handleSelectAll(e.target.checked)}
                    className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                  />
                </th>
              )}
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-16 bg-gray-100">
                STT
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32">
                SỐ VĂN THƯ
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-36">
                NGÀY NHẬN VĂN THƯ
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-28">
                LOẠI ÁN
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32">
                SỐ THỤ LÝ
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32">
                NGÀY THỤ LÝ
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32">
                TAND
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32">
                SỐ BẢN ÁN
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-36">
                NGÀY BAN HÀNH BẢN ÁN
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-44">
                BỊ CÁO/NĐ/NKK
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-44">
                TỘI DANH/BĐ/NBK
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-44">
                TỘI DANH/QHPL
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32">
                HÌNH THỨC
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-28">
                THỦ TỤC
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32">
                THẨM PHÁN
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-44">
                TRƯỞNG/PHÓ PHÒNG KTNV/THẨM TRA VIÊN
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32">
                GHI CHÚ
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32">
                GHI CHÚ KQ
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-32">
                TRẠNG THÁI
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-44">
                {onSort ? (
                  <SortButton field="soNgayConLai">
                    THỜI HẠN 90 NGÀY VỚI NGÀY NHẬN VĂN THƯ
                  </SortButton>
                ) : (
                  'THỜI HẠN 90 NGÀY VỚI NGÀY NHẬN VĂN THƯ'
                )}
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-44">
                THỜI GIAN CÒN LẠI CỦA THỜI HẠN 90 NGÀY NHẬN VĂN THƯ
              </th>
              <th className="px-4 py-4 text-left text-xs font-bold text-gray-700 uppercase tracking-wider w-28">
                Thao tác
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-100">
            {cases.length === 0 ? (
              <tr>
                <td colSpan={hasPermission('court_case_delete') ? 23 : 22} className="px-4 py-12 text-center">
                  <div className="flex flex-col items-center justify-center space-y-3">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <div>
                      <p className="text-lg font-medium text-gray-900">Không có vụ việc nào</p>
                      <p className="text-sm text-gray-500 mt-1">Thử điều chỉnh bộ lọc hoặc thêm vụ việc mới</p>
                    </div>
                  </div>
                </td>
              </tr>
            ) : (
              cases.map((courtCase, index) => (
                <tr key={courtCase._id} className="hover:bg-blue-50 transition-colors duration-150 group">
                  {hasPermission('court_case_delete') && (
                    <td className="px-4 py-4">
                      <input
                        type="checkbox"
                        checked={selectedCases.includes(courtCase._id)}
                        onChange={(e) => handleSelectCase(courtCase._id, e.target.checked)}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2"
                      />
                    </td>
                  )}
                  <td className="px-4 py-4 text-center">
                    <span className="text-sm font-medium text-gray-900">
                      {courtCase.stt}
                    </span>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-900 truncate" title={courtCase.soVanThu}>
                      {courtCase.soVanThu || (
                        <span className="text-gray-400 italic">Chưa có</span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-900 font-medium">
                      {formatDate(courtCase.ngayNhanVanThu)}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="flex items-center space-x-1">
                      <span className="text-sm font-medium text-gray-600">
                        {getTypeIcon(courtCase.loaiAn)}
                      </span>
                      <span className="text-sm text-gray-900">
                        {courtCase.loaiAn}
                      </span>
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-900 font-medium truncate" title={courtCase.soThuLy}>
                      {courtCase.soThuLy || (
                        <span className="text-gray-400 italic">Chưa có</span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-900 font-medium">
                      {formatDate(courtCase.ngayThuLy)}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-900 truncate" title={courtCase.tand}>
                      {courtCase.tand || (
                        <span className="text-gray-400 italic">Chưa có</span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-900 font-medium truncate" title={courtCase.soBanAn}>
                      {courtCase.soBanAn || (
                        <span className="text-gray-400 italic">Chưa có</span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-900 font-medium">
                      {formatDate(courtCase.ngayBanHanh)}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-900 truncate" title={courtCase.biCaoNguoiKhieuKien}>
                      {courtCase.biCaoNguoiKhieuKien || (
                        <span className="text-gray-400 italic">Chưa có</span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-900 truncate" title={courtCase.toiDanhNoiDung}>
                      {courtCase.toiDanhNoiDung || (
                        <span className="text-gray-400 italic">Chưa có</span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-900 truncate" title={courtCase.quanHePhatLuat}>
                      {courtCase.quanHePhatLuat || (
                        <span className="text-gray-400 italic">Chưa có</span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-900 truncate" title={courtCase.hinhThucXuLy}>
                      {courtCase.hinhThucXuLy || (
                        <span className="text-gray-400 italic">Chưa có</span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <span className="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-gray-100 text-gray-700">
                      {courtCase.thuTucApDung || 'Chưa có'}
                    </span>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-900 truncate" title={courtCase.thamPhanPhuTrach}>
                      {courtCase.thamPhanPhuTrach || (
                        <span className="text-gray-400 italic">Chưa có</span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-900 truncate" title={courtCase.truongPhoPhongKTNV}>
                      {courtCase.truongPhoPhongKTNV || (
                        <span className="text-gray-400 italic">Chưa có</span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-900 truncate" title={courtCase.ghiChu}>
                      {courtCase.ghiChu || (
                        <span className="text-gray-400 italic">Chưa có</span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <div className="text-sm text-gray-900 truncate" title={courtCase.ghiChuKetQua}>
                      {courtCase.ghiChuKetQua || (
                        <span className="text-gray-400 italic">Chưa có</span>
                      )}
                    </div>
                  </td>
                  <td className="px-4 py-4">
                    <span className={`inline-flex items-center px-3 py-1 rounded-full text-xs font-semibold ${getStatusBadgeClass(courtCase.trangThaiGiaiQuyet)}`}>
                      {courtCase.trangThaiGiaiQuyet}
                    </span>
                  </td>
                  <td className="px-4 py-4">
                    {courtCase.ngayNhanVanThu && courtCase.thoiHan90NgayFormatted ? (
                      <div className="text-sm font-medium text-gray-900">
                        {courtCase.thoiHan90NgayFormatted}
                      </div>
                    ) : (
                      <span className="text-xs text-gray-400 italic">
                        Chưa có ngày nhận văn thư
                      </span>
                    )}
                  </td>
                  <td className="px-4 py-4">
                    {courtCase.ngayNhanVanThu && courtCase.trangThaiThoiHan ? (
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-semibold ${getDeadlineStatusClass(courtCase.trangThaiThoiHan)}`}>
                        {formatRemainingDays(courtCase.soNgayConLai, courtCase.trangThaiThoiHan)}
                      </span>
                    ) : (
                      <span className="text-xs text-gray-400 italic">
                        Chưa có
                      </span>
                    )}
                  </td>
                  <td className="px-4 py-4">
                    <div className="flex items-center space-x-1">
                      {hasPermission('court_case_view') && (
                        <button
                          onClick={() => onCaseSelect(courtCase)}
                          className="p-2 text-blue-600 hover:text-blue-800 hover:bg-blue-50 rounded-lg transition-all duration-150"
                          title="Xem chi tiết"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                          </svg>
                        </button>
                      )}
                      {hasPermission('court_case_edit') && (
                        <button
                          onClick={() => onCaseEdit(courtCase)}
                          className="p-2 text-green-600 hover:text-green-800 hover:bg-green-50 rounded-lg transition-all duration-150"
                          title="Chỉnh sửa"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                          </svg>
                        </button>
                      )}
                      {hasPermission('court_case_delete') && (
                        <button
                          onClick={() => onCaseDelete(courtCase._id)}
                          className="p-2 text-red-600 hover:text-red-800 hover:bg-red-50 rounded-lg transition-all duration-150"
                          title="Xóa"
                        >
                          <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                          </svg>
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>
    </div>
  );
};

export default CourtCaseList;
