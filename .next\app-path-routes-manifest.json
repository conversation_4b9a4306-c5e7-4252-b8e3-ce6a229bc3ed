{"/api/auth/login/route": "/api/auth/login", "/api/auth/logout/route": "/api/auth/logout", "/api/auth/route": "/api/auth", "/api/uploads/single/[...path]/route": "/api/uploads/single/[...path]", "/api/revalidate/route": "/api/revalidate", "/api/uploads/media/[...path]/route": "/api/uploads/media/[...path]", "/robots.txt/route": "/robots.txt", "/api/pdf-proxy/route": "/api/pdf-proxy", "/(auth)/forgot-pass/page": "/forgot-pass", "/(auth)/register/page": "/register", "/_not-found/page": "/_not-found", "/(auth)/change-password/page": "/change-password", "/(auth)/verify/page": "/verify", "/(auth)/2fa/page": "/2fa", "/(auth)/logout/page": "/logout", "/(auth)/login/page": "/login", "/author/page": "/author", "/logout-direct/page": "/logout-direct", "/page": "/", "/(private)/dashboard/account/page": "/dashboard/account", "/(private)/dashboard/court-cases/page": "/dashboard/court-cases", "/(private)/dashboard/manager/setting/page": "/dashboard/manager/setting", "/(private)/dashboard/user/[id]/page": "/dashboard/user/[id]", "/(private)/dashboard/page": "/dashboard", "/(private)/dashboard/setting/page": "/dashboard/setting", "/(private)/dashboard/files/page": "/dashboard/files", "/(private)/dashboard/user/import/page": "/dashboard/user/import", "/(private)/dashboard/user/add/page": "/dashboard/user/add", "/(private)/dashboard/user/log/[id]/page": "/dashboard/user/log/[id]", "/(private)/dashboard/user/page": "/dashboard/user"}