{"version": 3, "middleware": {"/": {"files": ["server/edge-runtime-webpack.js", "server/src/middleware.js"], "name": "src/middleware", "page": "/", "matchers": [{"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/dashboard(?:\\/((?:[^\\/#\\?]+?)(?:\\/(?:[^\\/#\\?]+?))*))?(\\.json)?[\\/#\\?]?$", "originalSource": "/dashboard/:path*"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/login(\\.json)?[\\/#\\?]?$", "originalSource": "/login"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/register(\\.json)?[\\/#\\?]?$", "originalSource": "/register"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/change-password(\\.json)?[\\/#\\?]?$", "originalSource": "/change-password"}, {"regexp": "^(?:\\/(_next\\/data\\/[^/]{1,}))?\\/forgot-pass(\\.json)?[\\/#\\?]?$", "originalSource": "/forgot-pass"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "oojIVS0GjSHPUg384RHjx", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "4zxNHyjT23uVHFst82Nv4ReLZMxgZZP1fRApR83hQrQ=", "__NEXT_PREVIEW_MODE_ID": "ea89f1616c7ac2d7c62109da25e6c17e", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "1e9c2cd4e21ddc1bb5d044726bae6a5f18c047f01685a4aa4e3ac01606220bb9", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "b8e7fb1ad8a7abd5d6f7c1dcc135c1b4a9f10946925e1b1bb28c380e0e3705b0"}}}, "functions": {}, "sortedMiddleware": ["/"]}