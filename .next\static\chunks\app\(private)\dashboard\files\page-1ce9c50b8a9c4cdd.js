(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[6156],{3348:(e,s,t)=>{"use strict";t.d(s,{U:()=>i,default:()=>n});var l=t(5155),a=t(2115);let r=(0,a.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),i=()=>(0,a.useContext)(r),n=e=>{let{children:s}=e,[t,i]=(0,a.useState)(()=>null),[n,c]=(0,a.useState)(!0),o=(0,a.useCallback)(e=>{i(e),localStorage.setItem("user",JSON.stringify(e))},[i]);return(0,a.useEffect)(()=>{let e=localStorage.getItem("user");i(e?JSON.parse(e):null),c(!1)},[i]),(0,l.jsx)(r.Provider,{value:{user:t,setUser:o,isAuthenticated:!!t,isLoading:n},children:s})}},4559:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var l=t(4556),a=t(9509);let r=l.Ik({NEXT_PUBLIC_API_ENDPOINT:l.Yj().url(),NEXT_PUBLIC_URL:l.Yj().url(),CRYPTOJS_SECRECT:l.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:a.env.CRYPTOJS_SECRECT});if(!r.success)throw console.error("Invalid environment variables:",r.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let i=r.data},4730:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>u});var l=t(5155),a=t(2115),r=t(8543),i=t(7937);let n={getFiles:(e,s)=>i.Ay.post("/api/administrator/files",e,{headers:{Authorization:"Bearer ".concat(s)}}),deleteFile:(e,s)=>i.Ay.delete("/api/administrator/files/".concat(e),{headers:{Authorization:"Bearer ".concat(s)}}),bulkAction:(e,s)=>i.Ay.post("/api/administrator/files/bulk-action",e,{headers:{Authorization:"Bearer ".concat(s)}}),getFileStats:e=>i.Ay.get("/api/administrator/files/stats",{headers:{Authorization:"Bearer ".concat(e)}}),getSyncStatus:e=>i.Ay.get("/api/administrator/files/sync-status",{headers:{Authorization:"Bearer ".concat(e)}}),syncExistingFiles:e=>i.Ay.post("/api/administrator/files/sync-existing",{},{headers:{Authorization:"Bearer ".concat(e)}})};var c=t(9828);let o=e=>{let{files:s,onFileSelect:t,onFileDelete:i,onBulkAction:n,loading:o=!1}=e,[d,x]=(0,a.useState)([]),[u,h]=(0,a.useState)(!1);(0,a.useEffect)(()=>{h(d.length===s.length&&s.length>0)},[d,s]);let m=e=>{if(0===d.length)return void r.oR.warning("Please select files first");confirm("delete"===e?"Are you sure you want to delete ".concat(d.length," files?"):"Are you sure you want to ".concat(e," ").concat(d.length," files?"))&&(n(d,e),x([]),h(!1))};return o?(0,l.jsxs)("div",{className:"flex justify-center items-center py-8",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,l.jsx)("span",{className:"ml-2",children:"Loading files..."})]}):0===s.length?(0,l.jsxs)("div",{className:"text-center py-8 text-gray-500",children:[(0,l.jsx)("div",{className:"text-4xl mb-2",children:"\uD83D\uDCC1"}),(0,l.jsx)("p",{children:"No files found"})]}):(0,l.jsxs)("div",{className:"bg-white rounded-lg shadow",children:[d.length>0&&(0,l.jsxs)("div",{className:"bg-blue-50 border-b px-4 py-3 flex items-center justify-between",children:[(0,l.jsxs)("span",{className:"text-sm text-blue-700",children:[d.length," files selected"]}),(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)("button",{onClick:()=>m("activate"),className:"px-3 py-1 bg-green-600 text-white rounded text-sm hover:bg-green-700",children:"Activate"}),(0,l.jsx)("button",{onClick:()=>m("deactivate"),className:"px-3 py-1 bg-yellow-600 text-white rounded text-sm hover:bg-yellow-700",children:"Deactivate"}),(0,l.jsx)("button",{onClick:()=>m("delete"),className:"px-3 py-1 bg-red-600 text-white rounded text-sm hover:bg-red-700",children:"Delete"})]})]}),(0,l.jsx)("div",{className:"overflow-x-auto",children:(0,l.jsxs)("table",{className:"w-full",children:[(0,l.jsx)("thead",{className:"bg-gray-50 border-b",children:(0,l.jsxs)("tr",{children:[(0,l.jsx)("th",{className:"px-4 py-3 text-left",children:(0,l.jsx)("input",{type:"checkbox",checked:u,onChange:()=>{u?x([]):x(s.map(e=>e._id)),h(!u)},className:"rounded border-gray-300"})}),(0,l.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"File"}),(0,l.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Type"}),(0,l.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Size"}),(0,l.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Uploaded By"}),(0,l.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Date"}),(0,l.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Status"}),(0,l.jsx)("th",{className:"px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider",children:"Actions"})]})}),(0,l.jsx)("tbody",{className:"bg-white divide-y divide-gray-200",children:s.map(e=>(0,l.jsxs)("tr",{className:"hover:bg-gray-50 ".concat(d.includes(e._id)?"bg-blue-50":""),children:[(0,l.jsx)("td",{className:"px-4 py-3",children:(0,l.jsx)("input",{type:"checkbox",checked:d.includes(e._id),onChange:()=>(e=>{x(s=>s.includes(e)?s.filter(s=>s!==e):[...s,e])})(e._id),className:"rounded border-gray-300"})}),(0,l.jsx)("td",{className:"px-4 py-3",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("span",{className:"text-2xl mr-3",children:((e,s)=>{switch(e){case"image":return"\uD83D\uDDBC️";case"video":return"\uD83C\uDFAC";case"document":if(s.includes("pdf"))return"\uD83D\uDCC4";if(s.includes("word"))return"\uD83D\uDCDD";if(s.includes("excel"))return"\uD83D\uDCCA";return"\uD83D\uDCC4";default:return"\uD83D\uDCC1"}})(e.type,e.mimetype)}),(0,l.jsxs)("div",{children:[(0,l.jsx)("div",{className:"text-sm font-medium text-gray-900 truncate max-w-xs",children:e.originalName}),(0,l.jsx)("div",{className:"text-xs text-gray-500 truncate max-w-xs",children:e.filename})]})]})}),(0,l.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,l.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full bg-gray-100 text-gray-800",children:e.type})}),(0,l.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,c.z3)(e.size)}),(0,l.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:e.uploadedBy.username}),(0,l.jsx)("td",{className:"px-4 py-3 text-sm text-gray-900",children:(0,c.Yq)(e.uploadedAt)}),(0,l.jsx)("td",{className:"px-4 py-3",children:(0,l.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(e.isActive?"bg-green-100 text-green-800":"bg-red-100 text-red-800"),children:e.isActive?"Active":"Inactive"})}),(0,l.jsx)("td",{className:"px-4 py-3 text-sm font-medium",children:(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)("button",{onClick:()=>t(e),className:"text-blue-600 hover:text-blue-900",children:"View"}),(0,l.jsx)("button",{onClick:()=>window.open(e.url,"_blank"),className:"text-green-600 hover:text-green-900",children:"Download"}),(0,l.jsx)("button",{onClick:()=>i(e._id),className:"text-red-600 hover:text-red-900",children:"Delete"})]})})]},e._id))})]})})]})},d=e=>{let{onSyncComplete:s}=e,[t,i]=(0,a.useState)(null),[o,d]=(0,a.useState)(!1),[x,u]=(0,a.useState)(!1),h=async()=>{try{let e=localStorage.getItem("sessionToken")||"",s=await n.getSyncStatus(e);s.payload.success&&i(s.payload.status)}catch(e){console.error("Error fetching sync status:",e)}},m=async()=>{if(!t||0===t.missingInDatabase)return void r.oR.info("Kh\xf4ng c\xf3 file n\xe0o cần đồng bộ");if(confirm("Bạn c\xf3 chắc muốn đồng bộ ".concat(t.missingInDatabase," files? Qu\xe1 tr\xecnh n\xe0y c\xf3 thể mất một \xedt thời gian.")))try{d(!0);let e=localStorage.getItem("sessionToken")||"";(await n.syncExistingFiles(e)).payload.success?(r.oR.success("Đ\xe3 bắt đầu đồng bộ file. Vui l\xf2ng kiểm tra lại sau v\xe0i ph\xfat."),setTimeout(()=>{h(),null==s||s()},3e3)):r.oR.error("Kh\xf4ng thể bắt đầu đồng bộ file")}catch(e){console.error("Error syncing files:",e),r.oR.error("Đ\xe3 xảy ra lỗi khi đồng bộ file")}finally{d(!1)}};if((0,a.useEffect)(()=>{h();let e=setInterval(h,3e4);return()=>clearInterval(e)},[]),!t)return(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,l.jsxs)("div",{className:"animate-pulse",children:[(0,l.jsx)("div",{className:"h-4 bg-gray-300 rounded w-1/4 mb-2"}),(0,l.jsx)("div",{className:"h-6 bg-gray-300 rounded w-1/2"})]})});let g=0===t.missingInDatabase;return(0,l.jsxs)("div",{className:"bg-white p-4 rounded-lg shadow",children:[(0,l.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"text-2xl mr-3",children:o?"\uD83D\uDD04":g?"✅":"⚠️"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"File Sync Status"}),(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Trạng th\xe1i đồng bộ giữa thư mục vật l\xfd v\xe0 database"})]})]}),(0,l.jsx)("button",{onClick:()=>u(!x),className:"text-blue-600 hover:text-blue-800 text-sm",children:x?"Ẩn chi tiết":"Xem chi tiết"})]}),(0,l.jsxs)("div",{className:"grid grid-cols-2 md:grid-cols-4 gap-4 mb-4",children:[(0,l.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-blue-600",children:t.totalPhysicalFiles}),(0,l.jsx)("div",{className:"text-xs text-gray-600",children:"Files vật l\xfd"})]}),(0,l.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded",children:[(0,l.jsx)("div",{className:"text-2xl font-bold text-green-600",children:t.totalDatabaseFiles}),(0,l.jsx)("div",{className:"text-xs text-gray-600",children:"Files trong DB"})]}),(0,l.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded",children:[(0,l.jsx)("div",{className:"text-2xl font-bold ".concat(t.missingInDatabase>0?"text-orange-600":"text-green-600"),children:t.missingInDatabase}),(0,l.jsx)("div",{className:"text-xs text-gray-600",children:"Chưa đồng bộ"})]}),(0,l.jsxs)("div",{className:"text-center p-3 bg-gray-50 rounded",children:[(0,l.jsx)("div",{className:"text-2xl font-bold ".concat(g?"text-green-600":"text-orange-600"),children:g?"100%":Math.round(t.totalDatabaseFiles/t.totalPhysicalFiles*100)+"%"}),(0,l.jsx)("div",{className:"text-xs text-gray-600",children:"Tỉ lệ sync"})]})]}),(0,l.jsx)("div",{className:"p-3 rounded-md mb-4 ".concat(g?"bg-green-50 text-green-800":"bg-orange-50 text-orange-800"),children:o?(0,l.jsxs)("span",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"}),"Đang đồng bộ files..."]}):g?"✅ Tất cả files đ\xe3 được đồng bộ":"⚠️ C\xf3 ".concat(t.missingInDatabase," files chưa được đồng bộ v\xe0o database")}),!g&&!o&&(0,l.jsxs)("button",{onClick:m,disabled:o,className:"w-full px-4 py-2 bg-orange-600 text-white rounded-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 disabled:opacity-50",children:["\uD83D\uDD04 Đồng bộ ",t.missingInDatabase," files"]}),x&&(0,l.jsxs)("div",{className:"mt-4 border-t pt-4",children:[(0,l.jsx)("h4",{className:"font-semibold text-gray-900 mb-2",children:"Files chưa đồng bộ (10 đầu ti\xean):"}),t.missingFiles&&t.missingFiles.length>0?(0,l.jsxs)("div",{className:"space-y-2 max-h-60 overflow-y-auto",children:[t.missingFiles.map((e,s)=>(0,l.jsxs)("div",{className:"flex items-center justify-between p-2 bg-gray-50 rounded text-sm",children:[(0,l.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,l.jsx)("div",{className:"truncate font-medium text-gray-900",children:e.filename}),(0,l.jsx)("div",{className:"text-gray-500 text-xs",children:e.relativePath})]}),(0,l.jsx)("div",{className:"ml-4 text-gray-600 text-xs",children:(0,c.z3)(e.size)})]},s)),t.missingInDatabase>t.missingFiles.length&&(0,l.jsxs)("div",{className:"text-center text-gray-500 text-sm py-2",children:["... v\xe0 ",t.missingInDatabase-t.missingFiles.length," files kh\xe1c"]})]}):(0,l.jsx)("div",{className:"text-gray-500 text-sm",children:"Kh\xf4ng c\xf3 files n\xe0o cần đồng bộ"})]}),(0,l.jsxs)("div",{className:"mt-4 flex justify-between items-center text-sm text-gray-500",children:[(0,l.jsxs)("span",{children:["Cập nhật lần cuối: ",new Date().toLocaleTimeString()]}),(0,l.jsx)("button",{onClick:h,className:"text-blue-600 hover:text-blue-800",children:"\uD83D\uDD04 L\xe0m mới"})]})]})};var x=t(7708);let u=()=>{let[e,s]=(0,a.useState)([]),[t,i]=(0,a.useState)(!0),[u,h]=(0,a.useState)(null),[m,g]=(0,a.useState)({page:1,perPage:20,type:"all",sortBy:"uploadedAt",sortOrder:"desc"}),[p,y]=(0,a.useState)(0),[f,b]=(0,a.useState)(null),[j,v]=(0,a.useState)(!1),N=async()=>{try{i(!0);let e=localStorage.getItem("sessionToken")||"",t=await n.getFiles(m,e);t.payload.success?(s(t.payload.files),y(t.payload.total||0)):r.oR.error("Failed to fetch files")}catch(e){console.error("Error fetching files:",e),r.oR.error("An error occurred while fetching files")}finally{i(!1)}},w=async()=>{try{let e=localStorage.getItem("sessionToken")||"",s=await n.getFileStats(e);s.payload.success&&h(s.payload.stats)}catch(e){console.error("Error fetching stats:",e)}},S=async e=>{if(confirm("Are you sure you want to delete this file?"))try{let s=localStorage.getItem("sessionToken")||"";(await n.deleteFile(e,s)).payload.success?(r.oR.success("File deleted successfully"),N(),w()):r.oR.error("Failed to delete file")}catch(e){console.error("Error deleting file:",e),r.oR.error("An error occurred while deleting file")}},k=async(e,s)=>{try{let t=localStorage.getItem("sessionToken")||"";(await n.bulkAction({fileIds:e,action:s},t)).payload.success?(r.oR.success("".concat(s," completed successfully")),N(),w()):r.oR.error("Failed to ".concat(s," files"))}catch(e){console.error("Error in bulk ".concat(s,":"),e),r.oR.error("An error occurred while performing ".concat(s))}},C=(e,s)=>{g(t=>({...t,[e]:s,page:1}))},D=e=>{g(s=>({...s,page:e}))};(0,a.useEffect)(()=>{N()},[m]),(0,a.useEffect)(()=>{w()},[]);let A=Math.ceil(p/m.perPage);return(0,l.jsx)(x.default,{requiredPermissions:["file_view","file_upload","file_delete"],requireAll:!1,children:(0,l.jsxs)("div",{className:"p-6",children:[(0,l.jsxs)("div",{className:"mb-6",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-900 mb-2",children:"File Management"}),(0,l.jsx)("p",{className:"text-gray-600",children:"Manage uploaded files and media"})]}),u&&(0,l.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-4 mb-6",children:[(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"text-2xl mr-3",children:"\uD83D\uDCC1"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Total Files"}),(0,l.jsx)("p",{className:"text-xl font-semibold",children:(0,c.ZV)(u.totalFiles)})]})]})}),(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"text-2xl mr-3",children:"\uD83D\uDCBE"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Total Size"}),(0,l.jsx)("p",{className:"text-xl font-semibold",children:(0,c.z3)(u.totalSize)})]})]})}),(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"text-2xl mr-3",children:"\uD83D\uDCC8"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"Recent Uploads"}),(0,l.jsx)("p",{className:"text-xl font-semibold",children:(0,c.ZV)(u.recentUploads)})]})]})}),(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg shadow",children:(0,l.jsxs)("div",{className:"flex items-center",children:[(0,l.jsx)("div",{className:"text-2xl mr-3",children:"\uD83C\uDFAF"}),(0,l.jsxs)("div",{children:[(0,l.jsx)("p",{className:"text-sm text-gray-600",children:"File Types"}),(0,l.jsx)("p",{className:"text-xl font-semibold",children:u.filesByType.length})]})]})})]}),(0,l.jsx)("div",{className:"mb-6",children:(0,l.jsx)(d,{onSyncComplete:()=>{N(),w()}})}),(0,l.jsx)("div",{className:"bg-white p-4 rounded-lg shadow mb-6",children:(0,l.jsxs)("div",{className:"flex flex-wrap gap-4 items-center",children:[(0,l.jsx)("div",{className:"flex-1 min-w-64",children:(0,l.jsx)("input",{type:"text",placeholder:"Search files...",className:"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",onChange:e=>{var s;return s=e.target.value,void g(e=>({...e,query:s,page:1}))}})}),(0,l.jsxs)("select",{value:m.type,onChange:e=>C("type",e.target.value),className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,l.jsx)("option",{value:"all",children:"All Types"}),(0,l.jsx)("option",{value:"image",children:"Images"}),(0,l.jsx)("option",{value:"video",children:"Videos"}),(0,l.jsx)("option",{value:"document",children:"Documents"}),(0,l.jsx)("option",{value:"other",children:"Other"})]}),(0,l.jsxs)("select",{value:"".concat(m.sortBy,"-").concat(m.sortOrder),onChange:e=>{let[s,t]=e.target.value.split("-");C("sortBy",s),C("sortOrder",t)},className:"px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500",children:[(0,l.jsx)("option",{value:"uploadedAt-desc",children:"Newest First"}),(0,l.jsx)("option",{value:"uploadedAt-asc",children:"Oldest First"}),(0,l.jsx)("option",{value:"filename-asc",children:"Name A-Z"}),(0,l.jsx)("option",{value:"filename-desc",children:"Name Z-A"}),(0,l.jsx)("option",{value:"size-desc",children:"Largest First"}),(0,l.jsx)("option",{value:"size-asc",children:"Smallest First"})]}),(0,l.jsx)("button",{onClick:()=>v(!0),className:"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500",children:"\uD83D\uDCE4 Upload File"})]})}),(0,l.jsx)(o,{files:e,onFileSelect:b,onFileDelete:S,onBulkAction:k,loading:t}),A>1&&(0,l.jsx)("div",{className:"mt-6 flex justify-center",children:(0,l.jsxs)("div",{className:"flex gap-2",children:[(0,l.jsx)("button",{onClick:()=>D(m.page-1),disabled:m.page<=1,className:"px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Previous"}),Array.from({length:Math.min(5,A)},(e,s)=>{let t=s+1;return(0,l.jsx)("button",{onClick:()=>D(t),className:"px-3 py-2 border rounded-md ".concat(m.page===t?"bg-blue-600 text-white border-blue-600":"border-gray-300 hover:bg-gray-50"),children:t},t)}),(0,l.jsx)("button",{onClick:()=>D(m.page+1),disabled:m.page>=A,className:"px-3 py-2 border border-gray-300 rounded-md disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50",children:"Next"})]})}),f&&(0,l.jsx)("div",{className:"fixed inset-0 flex items-center justify-center z-50",style:{backgroundColor:"rgba(0, 0, 0, 0.5)"},children:(0,l.jsxs)("div",{className:"p-6 rounded-lg max-w-2xl w-full mx-4 max-h-96 overflow-y-auto",style:{backgroundColor:"#ffffff",color:"#111827"},children:[(0,l.jsxs)("div",{className:"flex justify-between items-center mb-4",children:[(0,l.jsx)("h3",{className:"text-lg font-semibold",style:{color:"#111827"},children:"File Details"}),(0,l.jsx)("button",{onClick:()=>b(null),className:"text-xl font-bold p-1 rounded hover:bg-gray-100",style:{color:"#6b7280"},children:"✕"})]}),(0,l.jsxs)("div",{className:"space-y-3",style:{color:"#111827"},children:[(0,l.jsxs)("div",{style:{color:"#111827"},children:[(0,l.jsx)("strong",{style:{color:"#111827"},children:"Name:"}),(0,l.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:f.originalName})]}),(0,l.jsxs)("div",{style:{color:"#111827"},children:[(0,l.jsx)("strong",{style:{color:"#111827"},children:"Type:"}),(0,l.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:f.type})]}),(0,l.jsxs)("div",{style:{color:"#111827"},children:[(0,l.jsx)("strong",{style:{color:"#111827"},children:"Size:"}),(0,l.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:(0,c.z3)(f.size)})]}),(0,l.jsxs)("div",{style:{color:"#111827"},children:[(0,l.jsx)("strong",{style:{color:"#111827"},children:"Uploaded by:"}),(0,l.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:f.uploadedBy.username})]}),(0,l.jsxs)("div",{style:{color:"#111827"},children:[(0,l.jsx)("strong",{style:{color:"#111827"},children:"Upload date:"}),(0,l.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:new Date(f.uploadedAt).toLocaleString()})]}),(0,l.jsxs)("div",{style:{color:"#111827"},children:[(0,l.jsx)("strong",{style:{color:"#111827"},children:"Status:"}),(0,l.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:f.isActive?"Active":"Inactive"})]}),f.description&&(0,l.jsxs)("div",{style:{color:"#111827"},children:[(0,l.jsx)("strong",{style:{color:"#111827"},children:"Description:"}),(0,l.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:f.description})]}),f.tags&&f.tags.length>0&&(0,l.jsxs)("div",{style:{color:"#111827"},children:[(0,l.jsx)("strong",{style:{color:"#111827"},children:"Tags:"}),(0,l.jsx)("span",{style:{color:"#111827",marginLeft:"8px"},children:f.tags.join(", ")})]})]}),(0,l.jsxs)("div",{className:"mt-6 flex gap-2",children:[(0,l.jsx)("button",{onClick:()=>window.open(f.url,"_blank"),className:"px-4 py-2 rounded hover:opacity-90 transition-opacity",style:{backgroundColor:"#2563eb",color:"#ffffff"},children:"View/Download"}),(0,l.jsx)("button",{onClick:()=>{S(f._id),b(null)},className:"px-4 py-2 rounded hover:opacity-90 transition-opacity",style:{backgroundColor:"#dc2626",color:"#ffffff"},children:"Delete"})]})]})})]})})}},7708:(e,s,t)=>{"use strict";t.d(s,{default:()=>n});var l=t(5155),a=t(8497),r=t(5695),i=t(2115);function n(e){let{children:s,requiredPermission:t,requiredPermissions:n=[],requireAll:c=!1,fallbackPath:o="/dashboard"}=e,{hasPermission:d,hasAnyPermission:x,isAdmin:u,isLoading:h}=(0,a.S)(),m=(0,r.useRouter)();if((0,i.useEffect)(()=>{if(!h&&!u)(t?d(t):!(n.length>0)||(c?n.every(e=>d(e)):x(n)))||m.replace(o)},[d,x,u,h,t,n,c,o,m]),h)return(0,l.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,l.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(u)return(0,l.jsx)(l.Fragment,{children:s});return(t?d(t):!(n.length>0)||(c?n.every(e=>d(e)):x(n)))?(0,l.jsx)(l.Fragment,{children:s}):(0,l.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,l.jsxs)("div",{className:"text-center",children:[(0,l.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,l.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,l.jsx)("button",{onClick:()=>m.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}},7937:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>d});var l=t(4559),a=t(9434),r=t(5695);class i extends Error{constructor({status:e,payload:s}){super("Http Error"),this.status=e,this.payload=s}}class n extends i{constructor({status:e,payload:s}){super({status:e,payload:s}),this.status=e,this.payload=s}}let c=null,o=async(e,s,t)=>{let o;(null==t?void 0:t.body)instanceof FormData?o=t.body:(null==t?void 0:t.body)&&(o=JSON.stringify(t.body));let d=o instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(d.Authorization="Bearer ".concat(e))}let x=(null==t?void 0:t.baseUrl)===void 0?l.A.NEXT_PUBLIC_API_ENDPOINT:t.baseUrl,u=s.startsWith("/")?"".concat(x).concat(s):"".concat(x,"/").concat(s),h=await fetch(u,{...t,headers:{...d,...null==t?void 0:t.headers},body:o,method:e}),m=null,g=h.headers.get("content-type");if(g&&g.includes("application/json"))try{m=await h.json()}catch(e){console.error("Failed to parse JSON response:",e),m=null}else m=await h.text();let p={status:h.status,payload:m};if(!h.ok)if(404===h.status||403===h.status)throw new n(p);else if(401===h.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,r.redirect)("/logout?sessionToken=".concat(e))}else if(!c){c=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...d}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await c}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),c=null,location.href="/login"}}}else throw new i(p);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,a.Fd)(s))){let{token:e}=m;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,a.Fd)(s)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return p},d={get:(e,s)=>o("GET",e,s),post:(e,s,t)=>o("POST",e,{...t,body:s}),put:(e,s,t)=>o("PUT",e,{...t,body:s}),patch:(e,s,t)=>o("PATCH",e,{...t,body:s}),delete:(e,s)=>o("DELETE",e,{...s})}},8240:(e,s,t)=>{Promise.resolve().then(t.bind(t,4730))},8497:(e,s,t)=>{"use strict";t.d(s,{S:()=>a});var l=t(3348);let a=()=>{let{user:e,isLoading:s}=(0,l.U)();return{hasPermission:t=>{var l;return!s&&!!e&&("admin"===e.rule||(null==(l=e.permissions)?void 0:l.includes(t))||!1)},hasAnyPermission:t=>!s&&!!e&&("admin"===e.rule||t.some(s=>{var t;return null==(t=e.permissions)?void 0:t.includes(s)})),getAllPermissions:()=>s||!e?[]:"admin"===e.rule?["user_view","user_add","user_edit","user_delete","user_import_csv","file_view","file_upload","file_delete","system_settings_view","system_settings_edit","analytics_view","permissions_manage"]:e.permissions||[],userPermissions:(null==e?void 0:e.permissions)||[],isAdmin:!s&&(null==e?void 0:e.rule)==="admin",isLoading:s}}},9434:(e,s,t)=>{"use strict";t.d(s,{Fd:()=>i,cn:()=>r}),t(7937);var l=t(2596),a=t(9688);function r(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,a.QP)((0,l.$)(s))}t(8801);let i=e=>e.startsWith("/")?e.slice(1):e},9828:(e,s,t)=>{"use strict";t.d(s,{Yq:()=>a,ZV:()=>r,z3:()=>l});let l=function(e){let s=arguments.length>1&&void 0!==arguments[1]?arguments[1]:2;if(0===e)return"0 Bytes";let t=Math.floor(Math.log(e)/Math.log(1024));return parseFloat((e/Math.pow(1024,t)).toFixed(s<0?0:s))+" "+["Bytes","KB","MB","GB","TB","PB","EB","ZB","YB"][t]},a=e=>{if(!e)return"";let s=new Date(e);return isNaN(s.getTime())?"":s.toLocaleDateString("vi-VN",{year:"numeric",month:"2-digit",day:"2-digit"})},r=e=>e.toLocaleString()}},e=>{e.O(0,[9268,3235,8543,8441,5964,7358],()=>e(e.s=8240)),_N_E=e.O()}]);