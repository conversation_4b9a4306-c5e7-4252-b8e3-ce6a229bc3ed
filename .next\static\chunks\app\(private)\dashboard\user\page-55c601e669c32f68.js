(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4778],{1725:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(7937);let a={fetchUsers:(e,t)=>s.Ay.post("/api/administrator/users",e,{headers:{Authorization:"Bearer ".concat(t)}}),fetchLogs:(e,t)=>s.Ay.get("api/administrator/log/".concat(e),{headers:{Authorization:"Bearer ".concat(t)}}),deleteUser:(e,t)=>s.Ay.delete("api/administrator/users/".concat(e._id),{headers:{Authorization:"Bearer ".concat(t)}}),fetchUserById:(e,t,r)=>s.Ay.get("api/administrator/users/".concat(e),{headers:{Authorization:"Bearer ".concat(t)},signal:r}),CreateUser:(e,t)=>s.Ay.post("api/administrator/signup",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateUser:(e,t)=>s.Ay.put("api/administrator/change-info/",e,{headers:{Authorization:"Bearer ".concat(t)}}),updatePassUser:(e,t)=>s.Ay.put("api/administrator/users/change-pass/",e,{headers:{Authorization:"Bearer ".concat(t)}})}},2306:(e,t,r)=>{"use strict";r.d(t,{default:()=>h});var s=r(5155),a=r(2115),o=r(6268),n=r(1032),i=r(1725),l=r(8617),c=r(8543),d=r(5695);function u(e){let{currentPage:t,totalPages:r,onPageChange:a}=e;return(0,s.jsxs)("div",{className:"flex justify-between items-center mt-4",children:[(0,s.jsx)("button",{className:"px-4 py-2 bg-gray-200 rounded disabled:opacity-50",onClick:()=>a(Math.max(t-1,1)),disabled:1===t,children:"Previous"}),(0,s.jsxs)("span",{children:["Page ",t," / ",r]}),(0,s.jsx)("button",{className:"px-4 py-2 bg-gray-200 rounded disabled:opacity-50",onClick:()=>a(Math.min(t+1,r)),disabled:t===r,children:"Next"})]})}function h(){let[e,t]=(0,a.useState)([]),[r,h]=(0,a.useState)(1),[g,m]=(0,a.useState)(1),[p,y]=(0,a.useState)(!1),x=async()=>{y(!0);try{let e=localStorage.getItem("sessionToken")||"",s=await i.A.fetchUsers({page:r,perPage:20},e);if(console.log("Fetch users response:",s),s&&s.payload){let{total:e,users:r}=s.payload;t(r||[]),m(Math.ceil(e/20))}else console.warn("No payload in users response"),t([])}catch(e){console.error("Error fetching users:",e),t([]),c.oR.error("An error occurred while fetching users. Please try again.")}finally{y(!1)}};(0,a.useEffect)(()=>{x()},[r]);let f=async(r,s)=>{try{let a=localStorage.getItem("sessionToken")||"",o=await i.A.deleteUser(r,a);if(o.payload.success){let r=[...e];r.splice(s,1),t(r),c.oR.success("Delete successful!")}else console.error("Error deleting user:",o.payload),c.oR.error("Error deleting user. Please try again.")}catch(e){console.error("Unexpected error:",e),c.oR.error("An error occurred during deletion. Please try again.")}},v=async e=>{let t=e.private?"mở kho\xe1":"kho\xe1";if(confirm("Bạn c\xf3 chắc chắn muốn ".concat(t," t\xe0i khoản ").concat(e.username,"?")))try{let r=localStorage.getItem("sessionToken")||"";(await l.A.toggleUserPrivate(e._id,r)).payload.success?(c.oR.success("".concat(t.charAt(0).toUpperCase()+t.slice(1)," t\xe0i khoản th\xe0nh c\xf4ng")),x()):c.oR.error("Kh\xf4ng thể ".concat(t," t\xe0i khoản"))}catch(e){console.error("Error toggling user private status:",e),c.oR.error("C\xf3 lỗi xảy ra khi ".concat(t," t\xe0i khoản"))}},b=[{accessorKey:"username",header:"T\xean"},{accessorKey:"phonenumber",header:"Số điện thoại"},{accessorKey:"email",header:"Email"},{accessorKey:"rule",header:"Chức vụ"},{accessorKey:"private",header:"T\xecnh Trạng",cell:e=>{let{row:t}=e;return(0,s.jsx)("span",{className:"inline-flex px-2 py-1 text-xs font-semibold rounded-full ".concat(t.original.private?"bg-red-100 text-red-800":"bg-green-100 text-green-800"),children:t.original.private?"Đ\xe3 kho\xe1":"Hoạt động"})}},{accessorKey:"createdAt",header:"Ng\xe0y đăng",cell:e=>{let{row:t}=e;return new Date(t.original.createdAt).toLocaleDateString()}},{header:"H\xe0nh động",cell:e=>{let{row:t,rowIndex:r}=e,a=(0,d.useRouter)();return(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)("button",{onClick:()=>{a.push("/dashboard/user/".concat(t.original._id))},className:"px-3 py-1 text-sm bg-blue-500 text-white rounded-md hover:bg-blue-600",children:"Chỉnh sửa"}),(0,s.jsx)("button",{onClick:()=>v(t.original),className:"px-3 py-1 text-sm rounded-md ".concat(t.original.private?"bg-green-500 hover:bg-green-600 text-white":"bg-yellow-500 hover:bg-yellow-600 text-white"),children:t.original.private?"Mở kho\xe1":"Kho\xe1"}),(0,s.jsx)("button",{onClick:()=>f(t.original,t.index),className:"px-3 py-1 text-sm bg-red-500 text-white rounded-md hover:bg-red-600",children:"X\xf3a"})]})}}],N=(0,o.N4)({data:e||[],columns:b,getCoreRowModel:(0,n.HT)()});return p?(0,s.jsx)("div",{className:"w-full p-4 flex items-center justify-center",children:(0,s.jsx)("div",{className:"text-gray-600",children:"Đang tải dữ liệu..."})}):(0,s.jsx)("div",{className:"w-full p-4",children:0===e.length?(0,s.jsx)("div",{className:"text-center text-gray-600 py-8",children:"Kh\xf4ng c\xf3 dữ liệu người d\xf9ng"}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("table",{className:"table-auto w-full border-collapse border border-gray-300 bg-white",children:[(0,s.jsx)("thead",{children:N.getHeaderGroups().map(e=>(0,s.jsx)("tr",{className:"bg-gray-200",children:e.headers.map(e=>(0,s.jsx)("th",{className:"border border-gray-300 px-4 py-2 text-gray-800 font-semibold",children:(0,o.Kv)(e.column.columnDef.header,e.getContext())},e.id))},e.id))}),(0,s.jsx)("tbody",{children:N.getRowModel().rows.map(e=>(0,s.jsx)("tr",{className:"border border-gray-300 hover:bg-gray-50",children:e.getVisibleCells().map(e=>(0,s.jsx)("td",{className:"border border-gray-300 px-4 py-2 text-gray-900",children:(0,o.Kv)(e.column.columnDef.cell,e.getContext())},e.id))},e.id))})]}),(0,s.jsx)(u,{currentPage:r,totalPages:g,onPageChange:e=>h(e)})]})})}},3348:(e,t,r)=>{"use strict";r.d(t,{U:()=>n,default:()=>i});var s=r(5155),a=r(2115);let o=(0,a.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),n=()=>(0,a.useContext)(o),i=e=>{let{children:t}=e,[r,n]=(0,a.useState)(()=>null),[i,l]=(0,a.useState)(!0),c=(0,a.useCallback)(e=>{n(e),localStorage.setItem("user",JSON.stringify(e))},[n]);return(0,a.useEffect)(()=>{let e=localStorage.getItem("user");n(e?JSON.parse(e):null),l(!1)},[n]),(0,s.jsx)(o.Provider,{value:{user:r,setUser:c,isAuthenticated:!!r,isLoading:i},children:t})}},4559:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var s=r(4556),a=r(9509);let o=s.Ik({NEXT_PUBLIC_API_ENDPOINT:s.Yj().url(),NEXT_PUBLIC_URL:s.Yj().url(),CRYPTOJS_SECRECT:s.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:a.env.CRYPTOJS_SECRECT});if(!o.success)throw console.error("Invalid environment variables:",o.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let n=o.data},7708:(e,t,r)=>{"use strict";r.d(t,{default:()=>i});var s=r(5155),a=r(8497),o=r(5695),n=r(2115);function i(e){let{children:t,requiredPermission:r,requiredPermissions:i=[],requireAll:l=!1,fallbackPath:c="/dashboard"}=e,{hasPermission:d,hasAnyPermission:u,isAdmin:h,isLoading:g}=(0,a.S)(),m=(0,o.useRouter)();if((0,n.useEffect)(()=>{if(!g&&!h)(r?d(r):!(i.length>0)||(l?i.every(e=>d(e)):u(i)))||m.replace(c)},[d,u,h,g,r,i,l,c,m]),g)return(0,s.jsx)("div",{className:"flex justify-center items-center min-h-[200px]",children:(0,s.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900"})});if(h)return(0,s.jsx)(s.Fragment,{children:t});return(r?d(r):!(i.length>0)||(l?i.every(e=>d(e)):u(i)))?(0,s.jsx)(s.Fragment,{children:t}):(0,s.jsx)("div",{className:"flex items-center justify-center min-h-screen",children:(0,s.jsxs)("div",{className:"text-center",children:[(0,s.jsx)("h1",{className:"text-2xl font-bold text-gray-800 mb-4",children:"Kh\xf4ng c\xf3 quyền truy cập"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Bạn kh\xf4ng c\xf3 quyền truy cập v\xe0o trang n\xe0y."}),(0,s.jsx)("button",{onClick:()=>m.back(),className:"px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600",children:"Quay lại"})]})})}},7937:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d});var s=r(4559),a=r(9434),o=r(5695);class n extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class i extends n{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let l=null,c=async(e,t,r)=>{let c;(null==r?void 0:r.body)instanceof FormData?c=r.body:(null==r?void 0:r.body)&&(c=JSON.stringify(r.body));let d=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(d.Authorization="Bearer ".concat(e))}let u=(null==r?void 0:r.baseUrl)===void 0?s.A.NEXT_PUBLIC_API_ENDPOINT:r.baseUrl,h=t.startsWith("/")?"".concat(u).concat(t):"".concat(u,"/").concat(t),g=await fetch(h,{...r,headers:{...d,...null==r?void 0:r.headers},body:c,method:e}),m=null,p=g.headers.get("content-type");if(p&&p.includes("application/json"))try{m=await g.json()}catch(e){console.error("Failed to parse JSON response:",e),m=null}else m=await g.text();let y={status:g.status,payload:m};if(!g.ok)if(404===g.status||403===g.status)throw new i(y);else if(401===g.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,o.redirect)("/logout?sessionToken=".concat(e))}else if(!l){l=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...d}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await l}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),l=null,location.href="/login"}}}else throw new n(y);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,a.Fd)(t))){let{token:e}=m;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,a.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return y},d={get:(e,t)=>c("GET",e,t),post:(e,t,r)=>c("POST",e,{...r,body:t}),put:(e,t,r)=>c("PUT",e,{...r,body:t}),patch:(e,t,r)=>c("PATCH",e,{...r,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},8497:(e,t,r)=>{"use strict";r.d(t,{S:()=>a});var s=r(3348);let a=()=>{let{user:e,isLoading:t}=(0,s.U)();return{hasPermission:r=>{var s;return!t&&!!e&&("admin"===e.rule||(null==(s=e.permissions)?void 0:s.includes(r))||!1)},hasAnyPermission:r=>!t&&!!e&&("admin"===e.rule||r.some(t=>{var r;return null==(r=e.permissions)?void 0:r.includes(t)})),getAllPermissions:()=>t||!e?[]:"admin"===e.rule?["user_view","user_add","user_edit","user_delete","user_import_csv","file_view","file_upload","file_delete","system_settings_view","system_settings_edit","analytics_view","permissions_manage"]:e.permissions||[],userPermissions:(null==e?void 0:e.permissions)||[],isAdmin:!t&&(null==e?void 0:e.rule)==="admin",isLoading:t}}},8617:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var s=r(7937);let a={getDashboardStats:e=>s.Ay.get("/api/administrator/dashboard-stats",{headers:{Authorization:"Bearer ".concat(e)}}),getRecentActivities:(e,t)=>s.Ay.get("/api/administrator/recent-activities".concat(t?"?limit=".concat(t):""),{headers:{Authorization:"Bearer ".concat(e)}}),toggleUserPrivate:(e,t)=>s.Ay.put("/api/administrator/update-private",{id:e},{headers:{Authorization:"Bearer ".concat(t)}})}},9434:(e,t,r)=>{"use strict";r.d(t,{Fd:()=>n,cn:()=>o}),r(7937);var s=r(2596),a=r(9688);function o(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,s.$)(t))}r(8801);let n=e=>e.startsWith("/")?e.slice(1):e},9970:(e,t,r)=>{Promise.resolve().then(r.bind(r,7708)),Promise.resolve().then(r.bind(r,2306))}},e=>{e.O(0,[9268,3235,8543,6268,8441,5964,7358],()=>e(e.s=9970)),_N_E=e.O()}]);