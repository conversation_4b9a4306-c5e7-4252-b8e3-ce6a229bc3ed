(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8152],{1354:(e,s,t)=>{Promise.resolve().then(t.bind(t,2831))},2831:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>v});var a=t(5155),l=t(2115),i=t(7703),r=t(8617),n=t(8543),c=t(3348),o=t(8497),d=t(4494),h=t(2773),m=t(5512);function x(){var e;let{user:s}=(0,c.U)(),{hasPermission:t}=(0,o.S)();return(0,a.jsxs)("div",{className:"p-6",children:[(0,a.jsxs)("div",{className:"mb-8",children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:["Ch\xe0o mừng, ",null==s?void 0:s.username,"!"]}),(0,a.jsx)("p",{className:"text-gray-600",children:"Đ\xe2y l\xe0 bảng điều khiển c\xe1 nh\xe2n của bạn."})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8",children:[(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 border border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"p-3 bg-blue-100 rounded-lg",children:(0,a.jsx)(d.A,{className:"h-6 w-6 text-blue-600"})}),(0,a.jsx)("h3",{className:"ml-3 text-lg font-semibold text-gray-900",children:"Th\xf4ng tin t\xe0i khoản"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Xem v\xe0 chỉnh sửa th\xf4ng tin c\xe1 nh\xe2n của bạn"}),(0,a.jsx)("a",{href:"/dashboard/account",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors",children:"Chỉnh sửa"})]}),(t("file_view")||t("file_upload"))&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 border border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"p-3 bg-green-100 rounded-lg",children:(0,a.jsx)(h.A,{className:"h-6 w-6 text-green-600"})}),(0,a.jsx)("h3",{className:"ml-3 text-lg font-semibold text-gray-900",children:"Quản l\xfd File"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Xem v\xe0 quản l\xfd c\xe1c file của bạn"}),(0,a.jsx)("a",{href:"/dashboard/files",className:"inline-flex items-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors",children:"Xem File"})]}),(t("system_settings_view")||t("system_settings_edit"))&&(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 border border-gray-200",children:[(0,a.jsxs)("div",{className:"flex items-center mb-4",children:[(0,a.jsx)("div",{className:"p-3 bg-purple-100 rounded-lg",children:(0,a.jsx)(m.A,{className:"h-6 w-6 text-purple-600"})}),(0,a.jsx)("h3",{className:"ml-3 text-lg font-semibold text-gray-900",children:"C\xe0i đặt hệ thống"})]}),(0,a.jsx)("p",{className:"text-gray-600 mb-4",children:"Xem c\xe0i đặt hệ thống"}),(0,a.jsx)("a",{href:"/dashboard/setting",className:"inline-flex items-center px-4 py-2 bg-purple-600 text-white rounded-md hover:bg-purple-700 transition-colors",children:"Xem c\xe0i đặt"})]})]}),(0,a.jsxs)("div",{className:"bg-white rounded-lg shadow-md p-6 border border-gray-200",children:[(0,a.jsx)("h2",{className:"text-xl font-semibold text-gray-900 mb-4",children:"Th\xf4ng tin t\xe0i khoản"}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"T\xean người d\xf9ng"}),(0,a.jsx)("p",{className:"text-gray-900",children:(null==s?void 0:s.username)||"Chưa cập nhật"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Email"}),(0,a.jsx)("p",{className:"text-gray-900",children:(null==s?void 0:s.email)||"Chưa cập nhật"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Vai tr\xf2"}),(0,a.jsx)("p",{className:"text-gray-900 capitalize",children:(null==s?void 0:s.rule)||"user"})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("label",{className:"block text-sm font-medium text-gray-700 mb-1",children:"Số quyền được cấp"}),(0,a.jsxs)("p",{className:"text-gray-900",children:[(null==s||null==(e=s.permissions)?void 0:e.length)||0," quyền"]})]})]})]})]})}var g=t(1379),u=t(4332),p=t(4988),y=t(9698),b=t(4138),j=t(1190);let v=()=>{var e,s;let{hasPermission:t}=(0,o.S)();if(!t("analytics_view"))return(0,a.jsx)(x,{});let[d,v]=(0,l.useState)({totalUsers:0,activeUsers:0,analytics:{totalPageViews:0,homeViewsToday:0,totalViewsToday:0,weeklyViews:0,uniqueVisitorsToday:0,homeViewsGrowth:0}}),[N,f]=(0,l.useState)(!0),[w,T]=(0,l.useState)(""),[_,A]=(0,l.useState)(!1),[C,S]=(0,l.useState)([]),[V,k]=(0,l.useState)(!0),{user:P}=(0,c.U)();(0,l.useEffect)(()=>{T(localStorage.getItem("sessionToken")||"")},[]),(0,l.useEffect)(()=>{if(!w||_)return;let e=setTimeout(async()=>{try{f(!0),console.log("Fetching dashboard stats...");let e=await r.A.getDashboardStats(w);console.log("API Response:",e),e.payload.success?(console.log("Dashboard stats received:",e.payload.stats),v(e.payload.stats),A(!0),n.oR.success("Đ\xe3 tải thống k\xea th\xe0nh c\xf4ng")):n.oR.error("Kh\xf4ng thể tải thống k\xea dashboard")}catch(e){console.error("Error fetching dashboard stats:",e),v({totalUsers:156,activeUsers:89,analytics:{totalPageViews:15e3,homeViewsToday:120,totalViewsToday:460,weeklyViews:3200,uniqueVisitorsToday:85,homeViewsGrowth:12.5}}),A(!0),(null==e?void 0:e.status)===429?n.oR.warning("Qu\xe1 nhiều y\xeau cầu, sử dụng dữ liệu cache"):n.oR.warning("Lỗi API, sử dụng dữ liệu mẫu")}finally{f(!1)}},1e3);return()=>clearTimeout(e)},[w]),(0,l.useEffect)(()=>{if(!w)return;let e=setTimeout(async()=>{try{k(!0);let e=await r.A.getRecentActivities(w,8);e.payload.success?S(e.payload.activities):n.oR.error("Kh\xf4ng thể tải hoạt động gần đ\xe2y")}catch(e){console.error("Error fetching recent activities:",e),(null==e?void 0:e.status)===429?n.oR.warning("Qu\xe1 nhiều y\xeau cầu, vui l\xf2ng thử lại sau"):n.oR.error("Lỗi khi tải hoạt động gần đ\xe2y")}finally{k(!1)}},1500);return()=>clearTimeout(e)},[w]);let E=[{title:"Quản l\xfd th\xe0nh vi\xean",description:"Xem v\xe0 quản l\xfd t\xe0i khoản người d\xf9ng",href:"/dashboard/user",icon:(0,a.jsx)(g.A,{size:20}),color:"green"},{title:"Quản l\xfd vụ việc",description:"Quản l\xfd vụ việc t\xf2a \xe1n v\xe0 thụ l\xfd",href:"/dashboard/court-cases",icon:(0,a.jsx)(h.A,{size:20}),color:"purple"},{title:"C\xe0i đặt hệ thống",description:"Cấu h\xecnh v\xe0 t\xf9y chỉnh website",href:"/dashboard/setting",icon:(0,a.jsx)(m.A,{size:20}),color:"orange"},{title:"Quản l\xfd file",description:"Quản l\xfd t\xe0i liệu v\xe0 h\xecnh ảnh",href:"/dashboard/files",icon:(0,a.jsx)(h.A,{size:20}),color:"blue"}];return(0,a.jsx)("div",{className:"max-w-7xl mx-auto",children:(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsx)("div",{className:"bg-gradient-to-r from-purple-600 to-blue-600 rounded-xl text-white p-8",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsxs)("h1",{className:"text-3xl font-bold mb-2 flex items-center",children:[(0,a.jsx)(u.A,{className:"mr-3",size:36}),"Bảng điều khiển quản trị"]}),(0,a.jsx)("p",{className:"text-purple-100 text-lg",children:"Quản l\xfd to\xe0n bộ hệ thống v\xe0 nội dung website"})]}),(0,a.jsx)("div",{className:"hidden md:block",children:(0,a.jsx)("div",{className:"bg-white/10 backdrop-blur-sm rounded-lg p-4",children:(0,a.jsx)(p.A,{size:48,className:"text-white"})})})]})}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-6 gap-6",children:N?Array.from({length:6}).map((e,s)=>(0,a.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-sm border animate-pulse",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-20 mb-2"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16"})]}),(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-lg"})]})},s)):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.CN,{title:"Th\xe0nh vi\xean",value:d.totalUsers,icon:(0,a.jsx)(g.A,{size:24}),color:"green"}),(0,a.jsx)(i.CN,{title:"Đang hoạt động",value:d.activeUsers,icon:(0,a.jsx)(p.A,{size:24}),color:"blue"}),(0,a.jsx)(i.CN,{title:"Lượt xem h\xf4m nay",value:(null==(e=d.analytics)?void 0:e.homeViewsToday)||0,icon:(0,a.jsx)(y.A,{size:24}),color:"purple"}),(0,a.jsx)(i.CN,{title:"Tổng lượt xem",value:(null==(s=d.analytics)?void 0:s.totalPageViews)||0,icon:(0,a.jsx)(b.A,{size:24}),color:"orange"})]})}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Thống k\xea truy cập"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:N?Array.from({length:4}).map((e,s)=>(0,a.jsx)("div",{className:"bg-white rounded-xl p-6 shadow-sm border animate-pulse",children:(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-24 mb-2"}),(0,a.jsx)("div",{className:"h-8 bg-gray-200 rounded w-16"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-20 mt-2"})]}),(0,a.jsx)("div",{className:"w-12 h-12 bg-gray-200 rounded-lg"})]})},s)):(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(i.CN,{title:"Lượt xem trang chủ h\xf4m nay",value:d.analytics.homeViewsToday,icon:(0,a.jsx)(y.A,{size:24}),color:"blue",trend:{value:"".concat(d.analytics.homeViewsGrowth>0?"+":"").concat(d.analytics.homeViewsGrowth,"%"),isPositive:d.analytics.homeViewsGrowth>=0}}),(0,a.jsx)(i.CN,{title:"Lượt xem b\xe0i viết h\xf4m nay",value:d.analytics.totalViewsToday-d.analytics.homeViewsToday||0,icon:(0,a.jsx)(h.A,{size:24}),color:"green",trend:{value:"".concat(d.analytics.homeViewsGrowth>0?"+":"").concat(d.analytics.homeViewsGrowth,"%"),isPositive:d.analytics.homeViewsGrowth>=0}}),(0,a.jsx)(i.CN,{title:"Tổng lượt xem h\xf4m nay",value:d.analytics.totalViewsToday,icon:(0,a.jsx)(y.A,{size:24}),color:"purple"}),(0,a.jsx)(i.CN,{title:"Kh\xe1ch truy cập duy nhất",value:d.analytics.uniqueVisitorsToday,icon:(0,a.jsx)(g.A,{size:24}),color:"orange"})]})}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Thống k\xea tuần n\xe0y"}),(0,a.jsx)(j.A,{size:20,className:"text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Tổng lượt xem tuần"}),(0,a.jsx)("span",{className:"font-semibold text-lg",children:d.analytics.weeklyViews.toLocaleString()})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Tổng lượt xem tất cả"}),(0,a.jsx)("span",{className:"font-semibold text-lg",children:d.analytics.totalPageViews.toLocaleString()})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Trung b\xecnh/ng\xe0y"}),(0,a.jsx)("span",{className:"font-semibold text-lg",children:Math.round(d.analytics.weeklyViews/7).toLocaleString()})]})]})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Hiệu suất h\xf4m nay"}),(0,a.jsx)(b.A,{size:20,className:"text-gray-500"})]}),(0,a.jsxs)("div",{className:"space-y-3",children:[(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Trang chủ"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"font-semibold",children:d.analytics.homeViewsToday}),(0,a.jsxs)("span",{className:"text-sm ".concat(d.analytics.homeViewsGrowth>=0?"text-green-600":"text-red-600"),children:[d.analytics.homeViewsGrowth>0?"+":"",d.analytics.homeViewsGrowth,"%"]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"B\xe0i viết"}),(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("span",{className:"font-semibold",children:d.analytics.totalViewsToday-d.analytics.homeViewsToday||0}),(0,a.jsxs)("span",{className:"text-sm ".concat(d.analytics.homeViewsGrowth>=0?"text-green-600":"text-red-600"),children:[d.analytics.homeViewsGrowth>0?"+":"",d.analytics.homeViewsGrowth,"%"]})]})]}),(0,a.jsxs)("div",{className:"flex justify-between items-center",children:[(0,a.jsx)("span",{className:"text-gray-600",children:"Tỷ lệ tương t\xe1c"}),(0,a.jsxs)("span",{className:"font-semibold",children:[d.analytics.totalViewsToday>0?Math.round(d.analytics.uniqueVisitorsToday/d.analytics.totalViewsToday*100):0,"%"]})]})]})]})]})]}),(0,a.jsxs)("div",{children:[(0,a.jsx)("h2",{className:"text-2xl font-bold text-gray-900 mb-6",children:"Thao t\xe1c nhanh"}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6",children:E.map((e,s)=>(0,a.jsx)(i.Lz,{title:e.title,description:e.description,href:e.href,icon:e.icon,color:e.color},s))})]}),(0,a.jsxs)(i.Zp,{children:[(0,a.jsxs)("div",{className:"flex items-center justify-between mb-6",children:[(0,a.jsx)("h3",{className:"text-lg font-semibold text-gray-900",children:"Hoạt động gần đ\xe2y"}),(0,a.jsx)("a",{href:"#",className:"text-blue-600 hover:text-blue-700 text-sm font-medium",children:"Xem tất cả"})]}),(0,a.jsx)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4",children:V?Array.from({length:8}).map((e,s)=>(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg border border-gray-100 animate-pulse",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full bg-gray-200"}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("div",{className:"h-4 bg-gray-200 rounded w-3/4 mb-1"}),(0,a.jsx)("div",{className:"h-3 bg-gray-200 rounded w-1/2"})]})]},s)):C.length>0?C.map(e=>{let s=(e=>{let s=new Date,t=new Date(e),a=Math.floor((s.getTime()-t.getTime())/1e3);return a<60?"".concat(a," gi\xe2y trước"):a<3600?"".concat(Math.floor(a/60)," ph\xfat trước"):a<86400?"".concat(Math.floor(a/3600)," giờ trước"):a<2592e3?"".concat(Math.floor(a/86400)," ng\xe0y trước"):"".concat(Math.floor(a/2592e3)," th\xe1ng trước")})(e.time),t=(e=>{switch(e){case"post_created":case"page_created":return{color:"bg-green-500",icon:"create"};case"post_updated":case"page_updated":return{color:"bg-blue-500",icon:"update"};case"post_pending":return{color:"bg-orange-500",icon:"pending"};case"post_pending_updated":return{color:"bg-orange-400",icon:"pending"};case"user_registered":return{color:"bg-purple-500",icon:"user"};default:return{color:"bg-gray-500",icon:"default"}}})(e.type);return(0,a.jsxs)("div",{className:"flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50 border border-gray-100",children:[(0,a.jsx)("div",{className:"w-2 h-2 rounded-full ".concat(t.color)}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm text-gray-900",children:e.action}),(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:[e.title&&(0,a.jsx)("span",{className:"font-medium",children:e.title}),e.title&&" • ","bởi ",e.user," • ",s]})]})]},e.id)}):(0,a.jsxs)("div",{className:"col-span-full text-center py-8 text-gray-500",children:[(0,a.jsx)(p.A,{size:48,className:"mx-auto mb-2 opacity-50"}),(0,a.jsx)("p",{children:"Chưa c\xf3 hoạt động n\xe0o"})]})})]})]})})}},3348:(e,s,t)=>{"use strict";t.d(s,{U:()=>r,default:()=>n});var a=t(5155),l=t(2115);let i=(0,l.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),r=()=>(0,l.useContext)(i),n=e=>{let{children:s}=e,[t,r]=(0,l.useState)(()=>null),[n,c]=(0,l.useState)(!0),o=(0,l.useCallback)(e=>{r(e),localStorage.setItem("user",JSON.stringify(e))},[r]);return(0,l.useEffect)(()=>{let e=localStorage.getItem("user");r(e?JSON.parse(e):null),c(!1)},[r]),(0,a.jsx)(i.Provider,{value:{user:t,setUser:o,isAuthenticated:!!t,isLoading:n},children:s})}},4559:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var a=t(4556),l=t(9509);let i=a.Ik({NEXT_PUBLIC_API_ENDPOINT:a.Yj().url(),NEXT_PUBLIC_URL:a.Yj().url(),CRYPTOJS_SECRECT:a.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:l.env.CRYPTOJS_SECRECT});if(!i.success)throw console.error("Invalid environment variables:",i.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let r=i.data},7703:(e,s,t)=>{"use strict";t.d(s,{CN:()=>c,Lz:()=>o,Wu:()=>n,ZB:()=>r,Zp:()=>l,aR:()=>i});var a=t(5155);t(2115);let l=e=>{let{children:s,className:t="",padding:l="md",shadow:i="sm",hover:r=!1,onClick:n}=e;return(0,a.jsx)("div",{className:"\n        bg-white rounded-xl border border-gray-100\n        ".concat({none:"",sm:"p-4",md:"p-6",lg:"p-8"}[l],"\n        ").concat({none:"",sm:"shadow-sm",md:"shadow-md",lg:"shadow-lg"}[i],"\n        ").concat(r?"hover:shadow-lg transition-shadow duration-200":"","\n        ").concat(t,"\n      "),onClick:n,children:s})},i=e=>{let{children:s,className:t=""}=e;return(0,a.jsx)("div",{className:"border-b border-gray-100 pb-4 mb-6 ".concat(t),children:s})},r=e=>{let{children:s,className:t="",size:l="md"}=e;return(0,a.jsx)("h2",{className:"font-bold text-gray-900 ".concat({sm:"text-lg",md:"text-xl",lg:"text-2xl"}[l]," ").concat(t),children:s})},n=e=>{let{children:s,className:t=""}=e;return(0,a.jsx)("div",{className:t,children:s})},c=e=>{let{title:s,value:t,icon:i,trend:r,color:n="blue",onClick:c,clickable:o=!1}=e,d=(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium text-gray-600 mb-1",children:s}),(0,a.jsx)("p",{className:"text-2xl font-bold text-gray-900",children:"number"==typeof t?t.toLocaleString():t}),r&&(0,a.jsxs)("p",{className:"text-sm mt-2 flex items-center ".concat(r.isPositive?"text-green-600":"text-red-600"),children:[(0,a.jsx)("span",{className:"mr-1",children:r.isPositive?"↗":"↘"}),r.value]})]}),i&&(0,a.jsx)("div",{className:"p-3 rounded-lg ".concat({blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"}[n]),children:(0,a.jsx)("div",{className:"text-white",children:i})})]});return o&&c?(0,a.jsx)(l,{hover:!0,className:"relative overflow-hidden cursor-pointer transform hover:scale-105 transition-all duration-200 ".concat(o?"hover:shadow-lg":""),onClick:c,children:d}):(0,a.jsx)(l,{hover:!0,className:"relative overflow-hidden",children:d})},o=e=>{let{title:s,description:t,icon:i,onClick:r,href:n,color:c="blue"}=e,o=e=>{let{children:s}=e;return(0,a.jsx)(l,{hover:!0,className:"cursor-pointer transform hover:scale-105 transition-transform duration-200",children:s})},d=(0,a.jsxs)("div",{className:"flex items-start space-x-4",children:[i&&(0,a.jsx)("div",{className:"p-3 rounded-lg ".concat({blue:"bg-blue-500",green:"bg-green-500",purple:"bg-purple-500",orange:"bg-orange-500",red:"bg-red-500"}[c]," flex-shrink-0"),children:(0,a.jsx)("div",{className:"text-white",children:i})}),(0,a.jsxs)("div",{className:"flex-1",children:[(0,a.jsx)("h3",{className:"font-semibold text-gray-900 mb-1",children:s}),(0,a.jsx)("p",{className:"text-sm text-gray-600",children:t})]})]});return n?(0,a.jsx)("a",{href:n,children:(0,a.jsx)(o,{children:d})}):(0,a.jsx)("div",{onClick:r,children:(0,a.jsx)(o,{children:d})})}},7937:(e,s,t)=>{"use strict";t.d(s,{Ay:()=>d});var a=t(4559),l=t(9434),i=t(5695);class r extends Error{constructor({status:e,payload:s}){super("Http Error"),this.status=e,this.payload=s}}class n extends r{constructor({status:e,payload:s}){super({status:e,payload:s}),this.status=e,this.payload=s}}let c=null,o=async(e,s,t)=>{let o;(null==t?void 0:t.body)instanceof FormData?o=t.body:(null==t?void 0:t.body)&&(o=JSON.stringify(t.body));let d=o instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(d.Authorization="Bearer ".concat(e))}let h=(null==t?void 0:t.baseUrl)===void 0?a.A.NEXT_PUBLIC_API_ENDPOINT:t.baseUrl,m=s.startsWith("/")?"".concat(h).concat(s):"".concat(h,"/").concat(s),x=await fetch(m,{...t,headers:{...d,...null==t?void 0:t.headers},body:o,method:e}),g=null,u=x.headers.get("content-type");if(u&&u.includes("application/json"))try{g=await x.json()}catch(e){console.error("Failed to parse JSON response:",e),g=null}else g=await x.text();let p={status:x.status,payload:g};if(!x.ok)if(404===x.status||403===x.status)throw new n(p);else if(401===x.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,i.redirect)("/logout?sessionToken=".concat(e))}else if(!c){c=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...d}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await c}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),c=null,location.href="/login"}}}else throw new r(p);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,l.Fd)(s))){let{token:e}=g;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,l.Fd)(s)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return p},d={get:(e,s)=>o("GET",e,s),post:(e,s,t)=>o("POST",e,{...t,body:s}),put:(e,s,t)=>o("PUT",e,{...t,body:s}),patch:(e,s,t)=>o("PATCH",e,{...t,body:s}),delete:(e,s)=>o("DELETE",e,{...s})}},8497:(e,s,t)=>{"use strict";t.d(s,{S:()=>l});var a=t(3348);let l=()=>{let{user:e,isLoading:s}=(0,a.U)();return{hasPermission:t=>{var a;return!s&&!!e&&("admin"===e.rule||(null==(a=e.permissions)?void 0:a.includes(t))||!1)},hasAnyPermission:t=>!s&&!!e&&("admin"===e.rule||t.some(s=>{var t;return null==(t=e.permissions)?void 0:t.includes(s)})),getAllPermissions:()=>s||!e?[]:"admin"===e.rule?["user_view","user_add","user_edit","user_delete","user_import_csv","file_view","file_upload","file_delete","system_settings_view","system_settings_edit","analytics_view","permissions_manage"]:e.permissions||[],userPermissions:(null==e?void 0:e.permissions)||[],isAdmin:!s&&(null==e?void 0:e.rule)==="admin",isLoading:s}}},8617:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var a=t(7937);let l={getDashboardStats:e=>a.Ay.get("/api/administrator/dashboard-stats",{headers:{Authorization:"Bearer ".concat(e)}}),getRecentActivities:(e,s)=>a.Ay.get("/api/administrator/recent-activities".concat(s?"?limit=".concat(s):""),{headers:{Authorization:"Bearer ".concat(e)}}),toggleUserPrivate:(e,s)=>a.Ay.put("/api/administrator/update-private",{id:e},{headers:{Authorization:"Bearer ".concat(s)}})}},9434:(e,s,t)=>{"use strict";t.d(s,{Fd:()=>r,cn:()=>i}),t(7937);var a=t(2596),l=t(9688);function i(){for(var e=arguments.length,s=Array(e),t=0;t<e;t++)s[t]=arguments[t];return(0,l.QP)((0,a.$)(s))}t(8801);let r=e=>e.startsWith("/")?e.slice(1):e}},e=>{e.O(0,[9268,3235,8543,7068,8441,5964,7358],()=>e(e.s=1354)),_N_E=e.O()}]);