(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4736],{174:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(2115),s=r(8637),a=r.n(s);function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var l=(0,n.forwardRef)(function(e,t){var r=e.color,s=e.size,a=void 0===s?24:s,l=function(e,t){if(null==e)return{};var r,n,s=function(e,t){if(null==e)return{};var r,n,s={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(s[r]=e[r]);return s}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,["color","size"]);return n.createElement("svg",o({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),n.createElement("line",{x1:"12",y1:"2",x2:"12",y2:"6"}),n.createElement("line",{x1:"12",y1:"18",x2:"12",y2:"22"}),n.createElement("line",{x1:"4.93",y1:"4.93",x2:"7.76",y2:"7.76"}),n.createElement("line",{x1:"16.24",y1:"16.24",x2:"19.07",y2:"19.07"}),n.createElement("line",{x1:"2",y1:"12",x2:"6",y2:"12"}),n.createElement("line",{x1:"18",y1:"12",x2:"22",y2:"12"}),n.createElement("line",{x1:"4.93",y1:"19.07",x2:"7.76",y2:"16.24"}),n.createElement("line",{x1:"16.24",y1:"7.76",x2:"19.07",y2:"4.93"}))});l.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},l.displayName="Loader";let i=l},1612:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>c});var n={};r.r(n),r.d(n,{BRAND:()=>l.qt,DIRTY:()=>a.jm,EMPTY_PATH:()=>a.I3,INVALID:()=>a.uY,NEVER:()=>l.tm,OK:()=>a.OK,ParseStatus:()=>a.MY,Schema:()=>l.Sj,ZodAny:()=>l.Ml,ZodArray:()=>l.n,ZodBigInt:()=>l.Lr,ZodBoolean:()=>l.WF,ZodBranded:()=>l.eN,ZodCatch:()=>l.hw,ZodDate:()=>l.aP,ZodDefault:()=>l.Xi,ZodDiscriminatedUnion:()=>l.jv,ZodEffects:()=>l.k1,ZodEnum:()=>l.Vb,ZodError:()=>i.G,ZodFirstPartyTypeKind:()=>l.kY,ZodFunction:()=>l.CZ,ZodIntersection:()=>l.Jv,ZodIssueCode:()=>i.eq,ZodLazy:()=>l.Ih,ZodLiteral:()=>l.DN,ZodMap:()=>l.Ut,ZodNaN:()=>l.Tq,ZodNativeEnum:()=>l.WM,ZodNever:()=>l.iS,ZodNull:()=>l.PQ,ZodNullable:()=>l.l1,ZodNumber:()=>l.rS,ZodObject:()=>l.bv,ZodOptional:()=>l.Ii,ZodParsedType:()=>o.Zp,ZodPipeline:()=>l._c,ZodPromise:()=>l.$i,ZodReadonly:()=>l.EV,ZodRecord:()=>l.b8,ZodSchema:()=>l.lK,ZodSet:()=>l.Kz,ZodString:()=>l.ND,ZodSymbol:()=>l.K5,ZodTransformer:()=>l.BG,ZodTuple:()=>l.y0,ZodType:()=>l.aR,ZodUndefined:()=>l._Z,ZodUnion:()=>l.fZ,ZodUnknown:()=>l._,ZodVoid:()=>l.a0,addIssueToContext:()=>a.zn,any:()=>l.bz,array:()=>l.YO,bigint:()=>l.o,boolean:()=>l.zM,coerce:()=>l.au,custom:()=>l.Ie,date:()=>l.p6,datetimeRegex:()=>l.fm,defaultErrorMap:()=>s.su,discriminatedUnion:()=>l.gM,effect:()=>l.QZ,enum:()=>l.k5,function:()=>l.fH,getErrorMap:()=>s.$W,getParsedType:()=>o.CR,instanceof:()=>l.Nl,intersection:()=>l.E$,isAborted:()=>a.G4,isAsync:()=>a.xP,isDirty:()=>a.DM,isValid:()=>a.fn,late:()=>l.fn,lazy:()=>l.RZ,literal:()=>l.eu,makeIssue:()=>a.y7,map:()=>l.Tj,nan:()=>l.oi,nativeEnum:()=>l.fc,never:()=>l.Zm,null:()=>l.ch,nullable:()=>l.me,number:()=>l.ai,object:()=>l.Ik,objectUtil:()=>o.o6,oboolean:()=>l.yN,onumber:()=>l.p7,optional:()=>l.lq,ostring:()=>l.Di,pipeline:()=>l.Tk,preprocess:()=>l.vk,promise:()=>l.iv,quotelessJson:()=>i.WI,record:()=>l.g1,set:()=>l.hZ,setErrorMap:()=>s.pJ,strictObject:()=>l.re,string:()=>l.Yj,symbol:()=>l.HR,transformer:()=>l.Gu,tuple:()=>l.PV,undefined:()=>l.Vx,union:()=>l.KC,unknown:()=>l.L5,util:()=>o.ZS,void:()=>l.rI});var s=r(5722),a=r(3454),o=r(6227),l=r(4556),i=r(4028);let c=n},2523:(e,t,r)=>{"use strict";r.d(t,{p:()=>i});var n=r(5155),s=r(9434),a=r(9310),o=r(9698),l=r(2115);let i=l.forwardRef((e,t)=>{let{className:r,type:i,...c}=e,[u,d]=(0,l.useState)(!1);return(0,n.jsx)(n.Fragment,{children:(0,n.jsxs)("div",{className:"relative w-full",children:[(0,n.jsx)("input",{type:"password"===i&&u?"text":i,autoComplete:"password"===i?"new-password":"",className:(0,s.cn)("input input-bordered w-full rounded-md",r),ref:t,...c}),"password"===i&&(u?(0,n.jsx)(a.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>d(!u)}):(0,n.jsx)(o.A,{className:"absolute right-3 top-1/2 transform -translate-y-1/2 cursor-pointer",onClick:()=>d(!u)}))]})})});i.displayName="Input"},3348:(e,t,r)=>{"use strict";r.d(t,{U:()=>o,default:()=>l});var n=r(5155),s=r(2115);let a=(0,s.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),o=()=>(0,s.useContext)(a),l=e=>{let{children:t}=e,[r,o]=(0,s.useState)(()=>null),[l,i]=(0,s.useState)(!0),c=(0,s.useCallback)(e=>{o(e),localStorage.setItem("user",JSON.stringify(e))},[o]);return(0,s.useEffect)(()=>{let e=localStorage.getItem("user");o(e?JSON.parse(e):null),i(!1)},[o]),(0,n.jsx)(a.Provider,{value:{user:r,setUser:c,isAuthenticated:!!r,isLoading:l},children:t})}},4559:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(4556),s=r(9509);let a=n.Ik({NEXT_PUBLIC_API_ENDPOINT:n.Yj().url(),NEXT_PUBLIC_URL:n.Yj().url(),CRYPTOJS_SECRECT:n.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:s.env.CRYPTOJS_SECRECT});if(!a.success)throw console.error("Invalid environment variables:",a.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let o=a.data},5463:(e,t,r)=>{Promise.resolve().then(r.bind(r,6380))},5937:(e,t,r)=>{"use strict";r.d(t,{lV:()=>d,MJ:()=>b,zB:()=>p,eI:()=>y,lR:()=>x,C5:()=>g});var n=r(5155),s=r(2115),a=r(4624),o=r(2177),l=r(9434),i=r(7073);let c=(0,r(2085).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2"),u=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)(i.b,{ref:t,className:(0,l.cn)(c(),r),...s})});u.displayName=i.b.displayName;let d=o.Op,m=s.createContext({}),p=e=>{let{...t}=e;return(0,n.jsx)(m.Provider,{value:{name:t.name},children:(0,n.jsx)(o.xI,{...t})})},h=()=>{let e=s.useContext(m),t=s.useContext(f),{getFieldState:r,formState:n}=(0,o.xW)(),a=r(e.name,n);if(!e)throw Error("useFormField should be used within <FormField>");let{id:l}=t;return{id:l,name:e.name,formItemId:"".concat(l,"-form-item"),formDescriptionId:"".concat(l,"-form-item-description"),formMessageId:"".concat(l,"-form-item-message"),...a}},f=s.createContext({}),y=s.forwardRef((e,t)=>{let{className:r,...a}=e,o=s.useId();return(0,n.jsx)(f.Provider,{value:{id:o},children:(0,n.jsx)("div",{ref:t,className:(0,l.cn)("mb-4",r),...a})})});y.displayName="FormItem";let x=s.forwardRef((e,t)=>{let{className:r,...s}=e,{error:a,formItemId:o}=h();return(0,n.jsx)(u,{ref:t,className:(0,l.cn)(a&&"text-destructive",r),htmlFor:o,...s})});x.displayName="FormLabel";let b=s.forwardRef((e,t)=>{let{...r}=e,{error:s,formItemId:o,formDescriptionId:l,formMessageId:i}=h();return(0,n.jsx)(a.DX,{ref:t,id:o,"aria-describedby":s?"".concat(l," ").concat(i):"".concat(l),"aria-invalid":!!s,...r})});b.displayName="FormControl",s.forwardRef((e,t)=>{let{className:r,...s}=e,{formDescriptionId:a}=h();return(0,n.jsx)("p",{ref:t,id:a,className:(0,l.cn)("text-[0.8rem] text-muted-foreground",r),...s})}).displayName="FormDescription";let g=s.forwardRef((e,t)=>{let{className:r,children:s,...a}=e,{error:o,formMessageId:i}=h(),c=o?String(null==o?void 0:o.message):s;return c?(0,n.jsx)("p",{ref:t,id:i,className:(0,l.cn)("text-[0.8rem] font-medium text-red-600",r),...a,children:c}):null});g.displayName="FormMessage"},6380:(e,t,r)=>{"use strict";r.d(t,{default:()=>w});var n=r(5155),s=r(3560),a=r(2177),o=r(5937),l=r(2523),i=r(5695),c=r(2115),u=r(1612);u.Ay.object({user:u.Ay.object({_id:u.Ay.number(),username:u.Ay.string(),email:u.Ay.string(),rule:u.Ay.string(),permissions:u.Ay.array(u.Ay.string()).optional()}),message:u.Ay.string()}).strict(),u.Ay.object({user:u.Ay.object({_id:u.Ay.number(),username:u.Ay.string(),email:u.Ay.string(),rule:u.Ay.string(),isMail:u.Ay.boolean(),isAuthApp:u.Ay.boolean(),permissions:u.Ay.array(u.Ay.string()).optional()}),message:u.Ay.string()}).strict();let d=u.Ay.object({username:u.Ay.string().trim().min(2).max(256)}),m=u.Ay.object({password:u.Ay.string().min(6).max(100),newPassword:u.Ay.string().min(6).max(100),confirmPassword:u.Ay.string().min(6).max(100)}).superRefine((e,t)=>{let{confirmPassword:r,newPassword:n}=e;r!==n&&t.addIssue({code:"custom",message:"Confirm password incorrect",path:["confirmPassword"]})}),p=u.Ay.object({isMail:u.Ay.boolean()}),h=u.Ay.object({isAuthApp:u.Ay.boolean()}),f=u.Ay.object({token:u.Ay.string()});var y=r(7937);let x={updateMe:(e,t)=>y.Ay.put("/api/auth/user/profile",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateMePassword:(e,t)=>y.Ay.put("/api/auth/change-pass",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateMeAuth:(e,t)=>y.Ay.put("/api/auth/active-mail",e,{headers:{Authorization:"Bearer ".concat(t)}}),updateMeAuthApp:(e,t)=>y.Ay.put("/api/auth/active-authapp",e,{headers:{Authorization:"Bearer ".concat(t)}}),verifyToken:(e,t)=>y.Ay.put("/api/auth/verify-authapp",e,{headers:{Authorization:"Bearer ".concat(t)}})};var b=r(174),g=r(8543),j=r(3348),v=r(6766);let w=e=>{let{profile:t}=e;console.log(t);let{setUser:r}=(0,j.U)(),[u,y]=(0,c.useState)(!1);(0,i.useRouter)();let[w,A]=(0,c.useState)(""),[N,k]=(0,c.useState)(""),[P,O]=(0,c.useState)(""),E=(0,c.useRef)(null),I=(0,a.mN)({resolver:(0,s.u)(d),defaultValues:{username:t.username}}),S=(0,a.mN)({resolver:(0,s.u)(m),defaultValues:{newPassword:"",password:"",confirmPassword:""}}),C=(0,a.mN)({resolver:(0,s.u)(p),defaultValues:{isMail:t.isMail}}),T=(0,a.mN)({resolver:(0,s.u)(h),defaultValues:{isAuthApp:t.isAuthApp}}),R=(0,a.mN)({resolver:(0,s.u)(f),defaultValues:{token:""}});async function Z(e){if(!u){y(!0);try{let t=localStorage.getItem("sessionToken")||"",n=await x.updateMe(e,t);n&&(r(n.payload.user),g.oR.success("Profile update successful!"))}catch(e){console.log(e),g.oR.error("An error occurred during update your profile. Please try again.")}finally{y(!1)}}}async function M(e){if(!u){y(!0);try{let t=localStorage.getItem("sessionToken")||"";await x.updateMePassword(e,t)&&g.oR.success("Profile update successful!")}catch(e){g.oR.error("An error occurred during update your profile. Please try again.")}finally{y(!1)}}}async function _(e){if(!u){y(!0);try{let t=localStorage.getItem("sessionToken")||"";await x.updateMeAuth(e,t)&&g.oR.success("Profile update successful!")}catch(e){g.oR.error("An error occurred during update your profile. Please try again.")}finally{y(!1)}}}async function z(e){if(!u){y(!0);try{let t=localStorage.getItem("sessionToken")||"",r=await x.updateMeAuthApp(e,t);r&&(A(r.payload.qrCode),k(r.payload.secret),E.current&&E.current.showModal())}catch(e){g.oR.error("An error occurred during update your profile. Please try again.")}finally{y(!1)}}}async function B(e){if(!u){y(!0);try{let r=localStorage.getItem("sessionToken")||"";await x.verifyToken(e,r)&&(t.isAuthApp=!0,E.current&&E.current.close(),g.oR.success("Profile update successful!"))}catch(e){O("Invalid 2FA code")}finally{y(!1)}}}return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsx)(o.lV,{...I,children:(0,n.jsxs)("form",{onSubmit:I.handleSubmit(Z),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto",noValidate:!0,children:[(0,n.jsx)(o.lR,{children:"Email"}),(0,n.jsx)(o.MJ,{children:(0,n.jsx)(l.p,{placeholder:"shadcn",type:"email",value:t.email,readOnly:!0})}),(0,n.jsx)(o.C5,{}),(0,n.jsx)(o.zB,{control:I.control,name:"username",render:e=>{let{field:t}=e;return(0,n.jsxs)(o.eI,{children:[(0,n.jsx)(o.lR,{children:"Nick name"}),(0,n.jsx)(o.MJ,{children:(0,n.jsx)(l.p,{placeholder:"Nick name",type:"text",...t})}),(0,n.jsx)(o.C5,{})]})}}),(0,n.jsxs)("button",{disabled:!!u,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[u?(0,n.jsx)(b.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})}),(0,n.jsx)(o.lV,{...S,children:(0,n.jsxs)("form",{onSubmit:S.handleSubmit(M),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto",noValidate:!0,children:[(0,n.jsx)(o.zB,{control:S.control,name:"password",render:e=>{let{field:t}=e;return(0,n.jsxs)(o.eI,{children:[(0,n.jsx)(o.lR,{children:"Mật khẩu cũ"}),(0,n.jsx)(o.MJ,{children:(0,n.jsx)(l.p,{placeholder:"password",type:"password",...t})}),(0,n.jsx)(o.C5,{})]})}}),(0,n.jsx)(o.zB,{control:S.control,name:"newPassword",render:e=>{let{field:t}=e;return(0,n.jsxs)(o.eI,{children:[(0,n.jsx)(o.lR,{children:"Mật khẩu mới"}),(0,n.jsx)(o.MJ,{children:(0,n.jsx)(l.p,{placeholder:"password",type:"password",...t})}),(0,n.jsx)(o.C5,{})]})}}),(0,n.jsx)(o.zB,{control:S.control,name:"confirmPassword",render:e=>{let{field:t}=e;return(0,n.jsxs)(o.eI,{children:[(0,n.jsx)(o.lR,{children:"X\xe1c nhận mật khẩu"}),(0,n.jsx)(o.MJ,{children:(0,n.jsx)(l.p,{placeholder:"X\xe1c nhận mật khẩu",type:"password",...t})}),(0,n.jsx)(o.C5,{})]})}}),(0,n.jsxs)("button",{disabled:!!u,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[u?(0,n.jsx)(b.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})}),(0,n.jsxs)(o.lV,{...C,children:[(0,n.jsx)("h3",{className:"text-2xl",children:"Bảo mật 2 lớp bằng Email"}),(0,n.jsxs)("form",{onSubmit:C.handleSubmit(_),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto my-4",noValidate:!0,children:[(0,n.jsx)("span",{className:"text-sm mb-4",children:'"K\xedch hoạt t\xednh năng bảo mật bằng Email: Một m\xe3 x\xe1c thực sẽ được gửi đến email của bạn khi đăng nhập."'}),(0,n.jsx)(o.zB,{control:C.control,name:"isMail",render:e=>{var t;let{field:r}=e;return(0,n.jsxs)(o.eI,{className:"flex items-center gap-2 justify-between py-2",children:[(0,n.jsx)("span",{children:"K\xedch hoạt bảo mật Qua Mail"}),(0,n.jsx)(o.MJ,{children:(0,n.jsx)("input",{type:"checkbox",checked:null!=(t=r.value)&&t,onChange:e=>r.onChange(e.target.checked),className:"checkbox"})}),(0,n.jsx)(o.C5,{})]})}}),(0,n.jsxs)("button",{disabled:!!u,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[u?(0,n.jsx)(b.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})]}),(0,n.jsx)("h3",{className:"text-2xl",children:"Bảo mật 2 lớp bằng App "}),(0,n.jsx)("dialog",{id:"my_modal_1",className:"modal ",ref:E,children:(0,n.jsxs)("div",{className:"modal-box relative ",children:[(0,n.jsx)("h3",{className:"text-lg font-bold",children:"Set Up Two-Factor Authentication"}),(0,n.jsx)("p",{className:"py-4",children:"Qu\xe9t m\xe3 QR b\xean dưới bằng ứng dụng x\xe1c thực của bạn (v\xed dụ: Google Authenticator, Authy).."}),w?(0,n.jsx)("div",{className:"flex justify-center",children:(0,n.jsx)(v.default,{src:w,alt:"QR Code",width:200,height:200,priority:!0})}):(0,n.jsx)("p",{children:"Loading QR code..."}),(0,n.jsx)(o.lV,{...R,children:(0,n.jsxs)("form",{onSubmit:R.handleSubmit(B),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto",noValidate:!0,children:[(0,n.jsx)(o.zB,{control:R.control,name:"token",render:e=>{let{field:t}=e;return(0,n.jsxs)(o.eI,{children:[(0,n.jsx)(o.lR,{children:"Nhập M\xe3 2FA Code"}),(0,n.jsx)(o.MJ,{children:(0,n.jsx)(l.p,{placeholder:"2FA Cod",type:"text",...t})}),(0,n.jsx)(o.C5,{})]})}}),P&&(0,n.jsx)("p",{className:"text-red-500 mt-2",children:P}),(0,n.jsxs)("button",{disabled:!!u,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[u?(0,n.jsx)(b.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})}),(0,n.jsx)("form",{method:"dialog",children:(0,n.jsx)("button",{className:"btn btn-sm btn-circle btn-ghost absolute right-2 top-2",children:"✕"})})]})}),(0,n.jsx)(o.lV,{...T,children:(0,n.jsxs)("form",{onSubmit:T.handleSubmit(z),className:"space-y-2 max-w-[600px] flex-shrink-0 w-full mx-auto my-4",noValidate:!0,children:[(0,n.jsx)("span",{className:"text-sm mb-4",children:'"Khi bật 2FA, mỗi lần đăng nhập, bạn sẽ được y\xeau cầu sử dụng kh\xf3a bảo mật, nhập m\xe3 x\xe1c minh hoặc x\xe1c nhận đăng nhập từ thiết bị di động, t\xf9y theo phương thức bạn đ\xe3 chọn."'}),(0,n.jsx)(o.zB,{control:T.control,name:"isAuthApp",render:e=>{var t;let{field:r}=e;return(0,n.jsxs)(o.eI,{className:"flex items-center gap-2 justify-between py-2",children:[(0,n.jsx)("span",{children:"K\xedch hoạt bảo mật Qua ứng dụng"}),(0,n.jsx)(o.MJ,{children:(0,n.jsx)("input",{type:"checkbox",checked:null!=(t=r.value)&&t,onChange:e=>r.onChange(e.target.checked),className:"checkbox"})}),(0,n.jsx)(o.C5,{})]})}}),t.isAuthApp?null:(0,n.jsxs)("button",{disabled:!!u,type:"submit",className:"btn btn-primary mt-8 bg-blue-700 w-40 text-white mx-auto flex items-center",children:[u?(0,n.jsx)(b.A,{className:"animate-spin"}):"","X\xe1c nhận"]})]})})]})}},6654:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return s}});let n=r(2115);function s(e,t){let r=(0,n.useRef)(null),s=(0,n.useRef)(null);return(0,n.useCallback)(n=>{if(null===n){let e=r.current;e&&(r.current=null,e());let t=s.current;t&&(s.current=null,t())}else e&&(r.current=a(e,n)),t&&(s.current=a(t,n))},[e,t])}function a(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let r=e(t);return"function"==typeof r?r:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7073:(e,t,r)=>{"use strict";r.d(t,{b:()=>i});var n=r(2115);r(7650);var s=r(4624),a=r(5155),o=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,s.TL)(`Primitive.${t}`),o=n.forwardRef((e,n)=>{let{asChild:s,...o}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(s?r:t,{...o,ref:n})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{}),l=n.forwardRef((e,t)=>(0,a.jsx)(o.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));l.displayName="Label";var i=l},7937:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>u});var n=r(4559),s=r(9434),a=r(5695);class o extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class l extends o{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let i=null,c=async(e,t,r)=>{let c;(null==r?void 0:r.body)instanceof FormData?c=r.body:(null==r?void 0:r.body)&&(c=JSON.stringify(r.body));let u=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(u.Authorization="Bearer ".concat(e))}let d=(null==r?void 0:r.baseUrl)===void 0?n.A.NEXT_PUBLIC_API_ENDPOINT:r.baseUrl,m=t.startsWith("/")?"".concat(d).concat(t):"".concat(d,"/").concat(t),p=await fetch(m,{...r,headers:{...u,...null==r?void 0:r.headers},body:c,method:e}),h=null,f=p.headers.get("content-type");if(f&&f.includes("application/json"))try{h=await p.json()}catch(e){console.error("Failed to parse JSON response:",e),h=null}else h=await p.text();let y={status:p.status,payload:h};if(!p.ok)if(404===p.status||403===p.status)throw new l(y);else if(401===p.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,a.redirect)("/logout?sessionToken=".concat(e))}else if(!i){i=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...u}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await i}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),i=null,location.href="/login"}}}else throw new o(y);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,s.Fd)(t))){let{token:e}=h;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,s.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return y},u={get:(e,t)=>c("GET",e,t),post:(e,t,r)=>c("POST",e,{...r,body:t}),put:(e,t,r)=>c("PUT",e,{...r,body:t}),patch:(e,t,r)=>c("PATCH",e,{...r,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},9310:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(2115),s=r(8637),a=r.n(s);function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var l=(0,n.forwardRef)(function(e,t){var r=e.color,s=e.size,a=void 0===s?24:s,l=function(e,t){if(null==e)return{};var r,n,s=function(e,t){if(null==e)return{};var r,n,s={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(s[r]=e[r]);return s}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,["color","size"]);return n.createElement("svg",o({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),n.createElement("path",{d:"M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24"}),n.createElement("line",{x1:"1",y1:"1",x2:"23",y2:"23"}))});l.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},l.displayName="EyeOff";let i=l},9434:(e,t,r)=>{"use strict";r.d(t,{Fd:()=>o,cn:()=>a}),r(7937);var n=r(2596),s=r(9688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.QP)((0,n.$)(t))}r(8801);let o=e=>e.startsWith("/")?e.slice(1):e},9698:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var n=r(2115),s=r(8637),a=r.n(s);function o(){return(o=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var n in r)Object.prototype.hasOwnProperty.call(r,n)&&(e[n]=r[n])}return e}).apply(this,arguments)}var l=(0,n.forwardRef)(function(e,t){var r=e.color,s=e.size,a=void 0===s?24:s,l=function(e,t){if(null==e)return{};var r,n,s=function(e,t){if(null==e)return{};var r,n,s={},a=Object.keys(e);for(n=0;n<a.length;n++)r=a[n],t.indexOf(r)>=0||(s[r]=e[r]);return s}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(n=0;n<a.length;n++)r=a[n],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(s[r]=e[r])}return s}(e,["color","size"]);return n.createElement("svg",o({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},l),n.createElement("path",{d:"M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"}),n.createElement("circle",{cx:"12",cy:"12",r:"3"}))});l.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},l.displayName="Eye";let i=l}},e=>{e.O(0,[9268,3235,8543,2182,4750,8441,5964,7358],()=>e(e.s=5463)),_N_E=e.O()}]);