const mongoose = require('mongoose');

const courtCaseSchema = new mongoose.Schema({
  // Thông tin thụ lý
  stt: {
    type: Number,
    required: true,
    default: 1
  },
  soVanThu: {
    type: String,
    default: '' // S<PERSON> văn thư
  },
  ngayNhanVanThu: {
    type: Date,
    default: null // Ngày nhận văn thư
  },
  loaiAn: {
    type: String,
    default: ''
  },
  soThuLy: {
    type: String,
    unique: true,
    sparse: true, // Allows null/undefined values while maintaining uniqueness
    default: ''
  },
  ngayThuLy: {
    type: Date,
    default: null
  },
  tand: {
    type: String,
    default: '' // Tòa án nhân dân
  },

  // Thông tin bản án/quyết định
  soBanAn: {
    type: String,
    default: ''
  },
  ngayBanHanh: {
    type: Date,
    default: null
  },
  biCaoNguoiKhieuKien: {
    type: String,
    default: '' // B<PERSON> cáo/Nguyên đơn/Người khiếu kiện
  },
  toiDanhNoiDung: {
    type: String,
    default: '' // Tội danh/Bồi dưỡng/Nội dung khiếu kiện
  },

  quanHePhatLuat: {
    type: String,
    default: '' // Tội danh/Quan hệ pháp luật
  },
  hinhThucXuLy: {
    type: String,
    default: ''
  },
  thuTucApDung: {
    type: String,
    default: ''
  },
  thamPhanPhuTrach: {
    type: String,
    default: ''
  },
  truongPhoPhongKTNV: {
    type: String,
    default: '' // Trưởng/Phó phòng KTNV/Thẩm tra viên
  },

  // Thông tin bổ sung
  ghiChu: {
    type: String,
    default: ''
  },
  ghiChuKetQua: {
    type: String,
    default: ''
  },
  trangThaiGiaiQuyet: {
    type: String,
    required: true,
    enum: ['Chưa giải quyết', 'Đang giải quyết', 'Đã giải quyết'],
    default: 'Chưa giải quyết'
  },

  // Metadata
  createdBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  },
  updatedBy: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    default: null
  }
}, {
  timestamps: true // Tự động tạo createdAt và updatedAt
});

// Indexes for better performance
courtCaseSchema.index({ soVanThu: 1 });
courtCaseSchema.index({ ngayNhanVanThu: 1 });
courtCaseSchema.index({ soThuLy: 1 });
courtCaseSchema.index({ ngayThuLy: 1 });
courtCaseSchema.index({ loaiAn: 1 });
courtCaseSchema.index({ trangThaiGiaiQuyet: 1 });
courtCaseSchema.index({ createdAt: 1 });

// Virtual for formatted dates
courtCaseSchema.virtual('ngayNhanVanThuFormatted').get(function() {
  return this.ngayNhanVanThu ? this.ngayNhanVanThu.toLocaleDateString('vi-VN') : '';
});

courtCaseSchema.virtual('ngayThuLyFormatted').get(function() {
  return this.ngayThuLy ? this.ngayThuLy.toLocaleDateString('vi-VN') : '';
});

courtCaseSchema.virtual('ngayBanHanhFormatted').get(function() {
  return this.ngayBanHanh ? this.ngayBanHanh.toLocaleDateString('vi-VN') : '';
});

// Virtual for deadline calculation (90 days from ngayNhanVanThu in UTC+7)
courtCaseSchema.virtual('thoiHan90Ngay').get(function() {
  if (!this.ngayNhanVanThu) return null;
  
  // Add 90 days to ngayNhanVanThu
  const deadline = new Date(this.ngayNhanVanThu);
  deadline.setDate(deadline.getDate() + 90);
  
  return deadline;
});

courtCaseSchema.virtual('thoiHan90NgayFormatted').get(function() {
  if (!this.thoiHan90Ngay) return '';
  
  // Format to Vietnam timezone (UTC+7)
  return this.thoiHan90Ngay.toLocaleDateString('vi-VN', {
    timeZone: 'Asia/Ho_Chi_Minh',
    year: 'numeric',
    month: '2-digit',
    day: '2-digit'
  });
});

// Virtual for remaining days until deadline (based on ngayNhanVanThu)
courtCaseSchema.virtual('soNgayConLai').get(function() {
  if (!this.thoiHan90Ngay) return null;
  
  // Get current date in Vietnam timezone
  const now = new Date();
  const vietnamNow = new Date(now.toLocaleString("en-US", {timeZone: "Asia/Ho_Chi_Minh"}));
  
  // Calculate difference in days
  const diffTime = this.thoiHan90Ngay.getTime() - vietnamNow.getTime();
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  
  return diffDays;
});

// Virtual for deadline status
courtCaseSchema.virtual('trangThaiThoiHan').get(function() {
  if (!this.soNgayConLai) return null;
  
  if (this.soNgayConLai < 0) {
    return 'Quá hạn';
  } else if (this.soNgayConLai <= 30) {
    return 'Gần hết hạn'; // 30-0 ngày màu đỏ
  } else if (this.soNgayConLai <= 60) {
    return 'Sắp hết hạn'; // 60-31 ngày vàng  
  } else {
    return 'Còn thời gian'; // 90-61 ngày màu xanh
  }
});

// Ensure virtual fields are serialized
courtCaseSchema.set('toJSON', { virtuals: true });

module.exports = mongoose.model('CourtCase', courtCaseSchema);
