(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[8017],{668:e=>{!function(){"undefined"!=typeof __nccwpck_require__&&(__nccwpck_require__.ab="//");var t={};t.endianness=function(){return"LE"},t.hostname=function(){return"undefined"!=typeof location?location.hostname:""},t.loadavg=function(){return[]},t.uptime=function(){return 0},t.freemem=function(){return Number.MAX_VALUE},t.totalmem=function(){return Number.MAX_VALUE},t.cpus=function(){return[]},t.type=function(){return"Browser"},t.release=function(){return"undefined"!=typeof navigator?navigator.appVersion:""},t.networkInterfaces=t.getNetworkInterfaces=function(){return{}},t.arch=function(){return"javascript"},t.platform=function(){return"browser"},t.tmpdir=t.tmpDir=function(){return"/tmp"},t.EOL="\n",t.homedir=function(){return"/"},e.exports=t}()},1592:(e,t,r)=>{"use strict";r.d(t,{A:()=>o});var n=r(7937);r(7967).config();let o={login:e=>n.Ay.post("/api/auth/login",e),register:e=>n.Ay.post("/api/auth/signup",e),forgot:e=>n.Ay.put("/api/auth/app-forgot-pass",e),changepass:e=>n.Ay.put("/api/auth/app-reset-pass",e),auth:e=>n.Ay.post("/api/auth",e,{baseUrl:""}),checkCode:e=>n.Ay.get("/api/auth/check-code/".concat(e)),VerifyAppCode:e=>n.Ay.post("/api/auth/verify-app-code",e),VerifyCode:e=>n.Ay.post("/api/auth/verify-code",e),logoutFromNextServerToServer:e=>n.Ay.post("/api/auth/blacklist-token/",e,{headers:{Authorization:"Bearer ".concat(e.sessionToken)}}),logoutFromNextClientToNextServer:(e,t)=>n.Ay.post("/api/auth/logout",{force:e},{baseUrl:"",signal:t}),slideSessionFromNextServerToServer:e=>n.Ay.post("/auth/slide-session",{},{headers:{Authorization:"Bearer ".concat(e)}}),slideSessionFromNextClientToNextServer:()=>n.Ay.post("/api/auth/slide-session",{},{baseUrl:""})}},1807:e=>{!function(){"use strict";var t={114:function(e){function t(e){if("string"!=typeof e)throw TypeError("Path must be a string. Received "+JSON.stringify(e))}function r(e,t){for(var r,n="",o=0,a=-1,s=0,i=0;i<=e.length;++i){if(i<e.length)r=e.charCodeAt(i);else if(47===r)break;else r=47;if(47===r){if(a===i-1||1===s);else if(a!==i-1&&2===s){if(n.length<2||2!==o||46!==n.charCodeAt(n.length-1)||46!==n.charCodeAt(n.length-2)){if(n.length>2){var l=n.lastIndexOf("/");if(l!==n.length-1){-1===l?(n="",o=0):o=(n=n.slice(0,l)).length-1-n.lastIndexOf("/"),a=i,s=0;continue}}else if(2===n.length||1===n.length){n="",o=0,a=i,s=0;continue}}t&&(n.length>0?n+="/..":n="..",o=2)}else n.length>0?n+="/"+e.slice(a+1,i):n=e.slice(a+1,i),o=i-a-1;a=i,s=0}else 46===r&&-1!==s?++s:s=-1}return n}var n={resolve:function(){for(var e,n,o="",a=!1,s=arguments.length-1;s>=-1&&!a;s--)s>=0?n=arguments[s]:(void 0===e&&(e=""),n=e),t(n),0!==n.length&&(o=n+"/"+o,a=47===n.charCodeAt(0));if(o=r(o,!a),a)if(o.length>0)return"/"+o;else return"/";return o.length>0?o:"."},normalize:function(e){if(t(e),0===e.length)return".";var n=47===e.charCodeAt(0),o=47===e.charCodeAt(e.length-1);return(0!==(e=r(e,!n)).length||n||(e="."),e.length>0&&o&&(e+="/"),n)?"/"+e:e},isAbsolute:function(e){return t(e),e.length>0&&47===e.charCodeAt(0)},join:function(){if(0==arguments.length)return".";for(var e,r=0;r<arguments.length;++r){var o=arguments[r];t(o),o.length>0&&(void 0===e?e=o:e+="/"+o)}return void 0===e?".":n.normalize(e)},relative:function(e,r){if(t(e),t(r),e===r||(e=n.resolve(e))===(r=n.resolve(r)))return"";for(var o=1;o<e.length&&47===e.charCodeAt(o);++o);for(var a=e.length,s=a-o,i=1;i<r.length&&47===r.charCodeAt(i);++i);for(var l=r.length-i,c=s<l?s:l,u=-1,f=0;f<=c;++f){if(f===c){if(l>c){if(47===r.charCodeAt(i+f))return r.slice(i+f+1);else if(0===f)return r.slice(i+f)}else s>c&&(47===e.charCodeAt(o+f)?u=f:0===f&&(u=0));break}var p=e.charCodeAt(o+f);if(p!==r.charCodeAt(i+f))break;47===p&&(u=f)}var d="";for(f=o+u+1;f<=a;++f)(f===a||47===e.charCodeAt(f))&&(0===d.length?d+="..":d+="/..");return d.length>0?d+r.slice(i+u):(i+=u,47===r.charCodeAt(i)&&++i,r.slice(i))},_makeLong:function(e){return e},dirname:function(e){if(t(e),0===e.length)return".";for(var r=e.charCodeAt(0),n=47===r,o=-1,a=!0,s=e.length-1;s>=1;--s)if(47===(r=e.charCodeAt(s))){if(!a){o=s;break}}else a=!1;return -1===o?n?"/":".":n&&1===o?"//":e.slice(0,o)},basename:function(e,r){if(void 0!==r&&"string"!=typeof r)throw TypeError('"ext" argument must be a string');t(e);var n,o=0,a=-1,s=!0;if(void 0!==r&&r.length>0&&r.length<=e.length){if(r.length===e.length&&r===e)return"";var i=r.length-1,l=-1;for(n=e.length-1;n>=0;--n){var c=e.charCodeAt(n);if(47===c){if(!s){o=n+1;break}}else -1===l&&(s=!1,l=n+1),i>=0&&(c===r.charCodeAt(i)?-1==--i&&(a=n):(i=-1,a=l))}return o===a?a=l:-1===a&&(a=e.length),e.slice(o,a)}for(n=e.length-1;n>=0;--n)if(47===e.charCodeAt(n)){if(!s){o=n+1;break}}else -1===a&&(s=!1,a=n+1);return -1===a?"":e.slice(o,a)},extname:function(e){t(e);for(var r=-1,n=0,o=-1,a=!0,s=0,i=e.length-1;i>=0;--i){var l=e.charCodeAt(i);if(47===l){if(!a){n=i+1;break}continue}-1===o&&(a=!1,o=i+1),46===l?-1===r?r=i:1!==s&&(s=1):-1!==r&&(s=-1)}return -1===r||-1===o||0===s||1===s&&r===o-1&&r===n+1?"":e.slice(r,o)},format:function(e){var t,r;if(null===e||"object"!=typeof e)throw TypeError('The "pathObject" argument must be of type Object. Received type '+typeof e);return t=e.dir||e.root,r=e.base||(e.name||"")+(e.ext||""),t?t===e.root?t+r:t+"/"+r:r},parse:function(e){t(e);var r,n={root:"",dir:"",base:"",ext:"",name:""};if(0===e.length)return n;var o=e.charCodeAt(0),a=47===o;a?(n.root="/",r=1):r=0;for(var s=-1,i=0,l=-1,c=!0,u=e.length-1,f=0;u>=r;--u){if(47===(o=e.charCodeAt(u))){if(!c){i=u+1;break}continue}-1===l&&(c=!1,l=u+1),46===o?-1===s?s=u:1!==f&&(f=1):-1!==s&&(f=-1)}return -1===s||-1===l||0===f||1===f&&s===l-1&&s===i+1?-1!==l&&(0===i&&a?n.base=n.name=e.slice(1,l):n.base=n.name=e.slice(i,l)):(0===i&&a?(n.name=e.slice(1,s),n.base=e.slice(1,l)):(n.name=e.slice(i,s),n.base=e.slice(i,l)),n.ext=e.slice(s,l)),i>0?n.dir=e.slice(0,i-1):a&&(n.dir="/"),n},sep:"/",delimiter:":",win32:null,posix:null};n.posix=n,e.exports=n}},r={};function n(e){var o=r[e];if(void 0!==o)return o.exports;var a=r[e]={exports:{}},s=!0;try{t[e](a,a.exports,n),s=!1}finally{s&&delete r[e]}return a.exports}n.ab="//",e.exports=n(114)}()},2016:()=>{},2079:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>c});var n=r(5155),o=r(1592),a=r(3348),s=r(5695),i=r(2115);function l(){let e=(0,s.useRouter)(),t=(0,s.usePathname)(),{setUser:r}=(0,a.U)(),l=(0,s.useSearchParams)().get("sessionToken");return(0,i.useEffect)(()=>{let n=new AbortController().signal;l===localStorage.getItem("sessionToken")&&o.A.logoutFromNextClientToNextServer(!0,n).then(n=>{r(null),e.push("/login?redirectFrom=".concat(t))})},[l,e,t,r]),(0,n.jsx)("div",{className:"bg-gray-100 py-16 flex items-center justify-center",children:(0,n.jsx)("span",{className:"loading loading-dots loading-xl"})})}function c(){return(0,n.jsx)(i.Suspense,{children:(0,n.jsx)(l,{})})}},3196:(e,t,r)=>{Promise.resolve().then(r.bind(r,2079))},3348:(e,t,r)=>{"use strict";r.d(t,{U:()=>s,default:()=>i});var n=r(5155),o=r(2115);let a=(0,o.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),s=()=>(0,o.useContext)(a),i=e=>{let{children:t}=e,[r,s]=(0,o.useState)(()=>null),[i,l]=(0,o.useState)(!0),c=(0,o.useCallback)(e=>{s(e),localStorage.setItem("user",JSON.stringify(e))},[s]);return(0,o.useEffect)(()=>{let e=localStorage.getItem("user");s(e?JSON.parse(e):null),l(!1)},[s]),(0,n.jsx)(a.Provider,{value:{user:r,setUser:c,isAuthenticated:!!r,isLoading:i},children:t})}},4559:(e,t,r)=>{"use strict";r.d(t,{A:()=>s});var n=r(4556),o=r(9509);let a=n.Ik({NEXT_PUBLIC_API_ENDPOINT:n.Yj().url(),NEXT_PUBLIC_URL:n.Yj().url(),CRYPTOJS_SECRECT:n.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:o.env.CRYPTOJS_SECRECT});if(!a.success)throw console.error("Invalid environment variables:",a.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let s=a.data},6589:e=>{"use strict";e.exports=JSON.parse('{"name":"dotenv","version":"16.6.1","description":"Loads environment variables from .env file","main":"lib/main.js","types":"lib/main.d.ts","exports":{".":{"types":"./lib/main.d.ts","require":"./lib/main.js","default":"./lib/main.js"},"./config":"./config.js","./config.js":"./config.js","./lib/env-options":"./lib/env-options.js","./lib/env-options.js":"./lib/env-options.js","./lib/cli-options":"./lib/cli-options.js","./lib/cli-options.js":"./lib/cli-options.js","./package.json":"./package.json"},"scripts":{"dts-check":"tsc --project tests/types/tsconfig.json","lint":"standard","pretest":"npm run lint && npm run dts-check","test":"tap run --allow-empty-coverage --disable-coverage --timeout=60000","test:coverage":"tap run --show-full-coverage --timeout=60000 --coverage-report=text --coverage-report=lcov","prerelease":"npm test","release":"standard-version"},"repository":{"type":"git","url":"git://github.com/motdotla/dotenv.git"},"homepage":"https://github.com/motdotla/dotenv#readme","funding":"https://dotenvx.com","keywords":["dotenv","env",".env","environment","variables","config","settings"],"readmeFilename":"README.md","license":"BSD-2-Clause","devDependencies":{"@types/node":"^18.11.3","decache":"^4.6.2","sinon":"^14.0.1","standard":"^17.0.0","standard-version":"^9.5.0","tap":"^19.2.0","typescript":"^4.8.4"},"engines":{"node":">=12"},"browser":{"fs":false}}')},7937:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>u});var n=r(4559),o=r(9434),a=r(5695);class s extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class i extends s{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let l=null,c=async(e,t,r)=>{let c;(null==r?void 0:r.body)instanceof FormData?c=r.body:(null==r?void 0:r.body)&&(c=JSON.stringify(r.body));let u=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(u.Authorization="Bearer ".concat(e))}let f=(null==r?void 0:r.baseUrl)===void 0?n.A.NEXT_PUBLIC_API_ENDPOINT:r.baseUrl,p=t.startsWith("/")?"".concat(f).concat(t):"".concat(f,"/").concat(t),d=await fetch(p,{...r,headers:{...u,...null==r?void 0:r.headers},body:c,method:e}),h=null,g=d.headers.get("content-type");if(g&&g.includes("application/json"))try{h=await d.json()}catch(e){console.error("Failed to parse JSON response:",e),h=null}else h=await d.text();let v={status:d.status,payload:h};if(!d.ok)if(404===d.status||403===d.status)throw new i(v);else if(401===d.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,a.redirect)("/logout?sessionToken=".concat(e))}else if(!l){l=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...u}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await l}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),l=null,location.href="/login"}}}else throw new s(v);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,o.Fd)(t))){let{token:e}=h;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,o.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return v},u={get:(e,t)=>c("GET",e,t),post:(e,t,r)=>c("POST",e,{...r,body:t}),put:(e,t,r)=>c("PUT",e,{...r,body:t}),patch:(e,t,r)=>c("PATCH",e,{...r,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},7967:(e,t,r)=>{var n=r(9509),o=r(4134).Buffer;let a=r(2016),s=r(1807),i=r(668),l=r(8777),c=r(6589).version,u=/(?:^|^)\s*(?:export\s+)?([\w.-]+)(?:\s*=\s*?|:\s+?)(\s*'(?:\\'|[^'])*'|\s*"(?:\\"|[^"])*"|\s*`(?:\\`|[^`])*`|[^#\r\n]+)?\s*(?:#.*)?(?:$|$)/mg;function f(e){console.log(`[dotenv@${c}][DEBUG] ${e}`)}function p(e){console.log(`[dotenv@${c}] ${e}`)}function d(e){return e&&e.DOTENV_KEY&&e.DOTENV_KEY.length>0?e.DOTENV_KEY:n.env.DOTENV_KEY&&n.env.DOTENV_KEY.length>0?n.env.DOTENV_KEY:""}function h(e){let t=null;if(e&&e.path&&e.path.length>0)if(Array.isArray(e.path))for(let r of e.path)a.existsSync(r)&&(t=r.endsWith(".vault")?r:`${r}.vault`);else t=e.path.endsWith(".vault")?e.path:`${e.path}.vault`;else t=s.resolve(n.cwd(),".env.vault");return a.existsSync(t)?t:null}function g(e){return"~"===e[0]?s.join(i.homedir(),e.slice(1)):e}let v={configDotenv:function(e){let t,r=s.resolve(n.cwd(),".env"),o="utf8",i=!!(e&&e.debug),l=!e||!("quiet"in e)||e.quiet;e&&e.encoding?o=e.encoding:i&&f("No encoding is specified. UTF-8 is used by default");let c=[r];if(e&&e.path)if(Array.isArray(e.path))for(let t of(c=[],e.path))c.push(g(t));else c=[g(e.path)];let u={};for(let r of c)try{let t=v.parse(a.readFileSync(r,{encoding:o}));v.populate(u,t,e)}catch(e){i&&f(`Failed to load ${r} ${e.message}`),t=e}let d=n.env;if(e&&null!=e.processEnv&&(d=e.processEnv),v.populate(d,u,e),i||!l){let e=Object.keys(u).length,r=[];for(let e of c)try{let t=s.relative(n.cwd(),e);r.push(t)}catch(r){i&&f(`Failed to load ${e} ${r.message}`),t=r}p(`injecting env (${e}) from ${r.join(",")}`)}return t?{parsed:u,error:t}:{parsed:u}},_configVault:function(e){let t=!!(e&&e.debug),r=!e||!("quiet"in e)||e.quiet;(t||!r)&&p("Loading env from encrypted .env.vault");let o=v._parseVault(e),a=n.env;return e&&null!=e.processEnv&&(a=e.processEnv),v.populate(a,o,e),{parsed:o}},_parseVault:function(e){let t,r=h(e=e||{});e.path=r;let n=v.configDotenv(e);if(!n.parsed){let e=Error(`MISSING_DATA: Cannot parse ${r} for an unknown reason`);throw e.code="MISSING_DATA",e}let o=d(e).split(","),a=o.length;for(let e=0;e<a;e++)try{let r=o[e].trim(),a=function(e,t){let r;try{r=new URL(t)}catch(e){if("ERR_INVALID_URL"===e.code){let e=Error("INVALID_DOTENV_KEY: Wrong format. Must be in valid uri format like dotenv://:<EMAIL>/vault/.env.vault?environment=development");throw e.code="INVALID_DOTENV_KEY",e}throw e}let n=r.password;if(!n){let e=Error("INVALID_DOTENV_KEY: Missing key part");throw e.code="INVALID_DOTENV_KEY",e}let o=r.searchParams.get("environment");if(!o){let e=Error("INVALID_DOTENV_KEY: Missing environment part");throw e.code="INVALID_DOTENV_KEY",e}let a=`DOTENV_VAULT_${o.toUpperCase()}`,s=e.parsed[a];if(!s){let e=Error(`NOT_FOUND_DOTENV_ENVIRONMENT: Cannot locate environment ${a} in your .env.vault file.`);throw e.code="NOT_FOUND_DOTENV_ENVIRONMENT",e}return{ciphertext:s,key:n}}(n,r);t=v.decrypt(a.ciphertext,a.key);break}catch(t){if(e+1>=a)throw t}return v.parse(t)},config:function(e){if(0===d(e).length)return v.configDotenv(e);let t=h(e);if(!t){var r;return r=`You set DOTENV_KEY but you are missing a .env.vault file at ${t}. Did you forget to build it?`,console.log(`[dotenv@${c}][WARN] ${r}`),v.configDotenv(e)}return v._configVault(e)},decrypt:function(e,t){let r=o.from(t.slice(-64),"hex"),n=o.from(e,"base64"),a=n.subarray(0,12),s=n.subarray(-16);n=n.subarray(12,-16);try{let e=l.createDecipheriv("aes-256-gcm",r,a);return e.setAuthTag(s),`${e.update(n)}${e.final()}`}catch(n){let e=n instanceof RangeError,t="Invalid key length"===n.message,r="Unsupported state or unable to authenticate data"===n.message;if(e||t){let e=Error("INVALID_DOTENV_KEY: It must be 64 characters long (or more)");throw e.code="INVALID_DOTENV_KEY",e}if(r){let e=Error("DECRYPTION_FAILED: Please check your DOTENV_KEY");throw e.code="DECRYPTION_FAILED",e}throw n}},parse:function(e){let t,r={},n=e.toString();for(n=n.replace(/\r\n?/mg,"\n");null!=(t=u.exec(n));){let e=t[1],n=t[2]||"",o=(n=n.trim())[0];n=n.replace(/^(['"`])([\s\S]*)\1$/mg,"$2"),'"'===o&&(n=(n=n.replace(/\\n/g,"\n")).replace(/\\r/g,"\r")),r[e]=n}return r},populate:function(e,t,r={}){let n=!!(r&&r.debug),o=!!(r&&r.override);if("object"!=typeof t){let e=Error("OBJECT_REQUIRED: Please check the processEnv argument being passed to populate");throw e.code="OBJECT_REQUIRED",e}for(let r of Object.keys(t))Object.prototype.hasOwnProperty.call(e,r)?(!0===o&&(e[r]=t[r]),n&&(!0===o?f(`"${r}" is already defined and WAS overwritten`):f(`"${r}" is already defined and was NOT overwritten`))):e[r]=t[r]}};e.exports.configDotenv=v.configDotenv,e.exports._configVault=v._configVault,e.exports._parseVault=v._parseVault,e.exports.config=v.config,e.exports.decrypt=v.decrypt,e.exports.parse=v.parse,e.exports.populate=v.populate,e.exports=v},9434:(e,t,r)=>{"use strict";r.d(t,{Fd:()=>s,cn:()=>a}),r(7937);var n=r(2596),o=r(9688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,o.QP)((0,n.$)(t))}r(8801);let s=e=>e.startsWith("/")?e.slice(1):e}},e=>{e.O(0,[9268,3235,8441,5964,7358],()=>e(e.s=3196)),_N_E=e.O()}]);