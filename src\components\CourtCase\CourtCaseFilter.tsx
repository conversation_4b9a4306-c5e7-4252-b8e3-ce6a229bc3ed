"use client";

import { useState } from "react";
import {
  CourtCaseSearchType,
  TrangThaiGiaiQuyetEnum
} from "@/schemaValidations/courtCase.schema";

interface CourtCaseFilterProps {
  searchParams: CourtCaseSearchType;
  onSearch: (params: Partial<CourtCaseSearchType>) => void;
  onReset: () => void;
}

const CourtCaseFilter = ({ searchParams, onSearch, onReset }: CourtCaseFilterProps) => {
  const [isExpanded, setIsExpanded] = useState(false);
  
  // Count active filters
  const activeFiltersCount = Object.values({
    search: searchParams.search,
    loaiAn: searchParams.loaiAn,
    trangThaiGiaiQuyet: searchParams.trangThaiGiaiQuyet,
    thuTucApDung: searchParams.thuTucApDung,
    fromDate: searchParams.fromDate,
    toDate: searchParams.toDate
  }).filter(value => value && value !== '').length;

  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
  };

  return (
    <div className="bg-white rounded-lg shadow">
      {/* Filter Header - Always visible */}
      <div 
        className="flex items-center justify-between p-4 cursor-pointer hover:bg-gray-50 transition-colors"
        onClick={toggleExpanded}
      >
        <div className="flex items-center gap-3">
          <div className="text-xl">🔍</div>
          <div>
            <h3 className="text-lg font-semibold text-gray-900">
              Bộ lọc và tìm kiếm
            </h3>
            {activeFiltersCount > 0 && (
              <p className="text-sm text-blue-600">
                {activeFiltersCount} bộ lọc đang được áp dụng
              </p>
            )}
          </div>
        </div>
        
        <div className="flex items-center gap-2">
          {/* Quick actions when collapsed */}
          {!isExpanded && activeFiltersCount > 0 && (
            <button
              onClick={(e) => {
                e.stopPropagation();
                onReset();
              }}
              className="px-3 py-1 text-xs bg-gray-100 text-gray-600 rounded-full hover:bg-gray-200 transition-colors"
            >
              Xóa tất cả
            </button>
          )}
          
          {/* Expand/Collapse icon */}
          <div className={`transform transition-transform duration-200 ${isExpanded ? 'rotate-180' : ''}`}>
            <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
      </div>

      {/* Filter Content - Expandable */}
      <div className={`transition-all duration-300 ease-in-out ${isExpanded ? 'max-h-96 opacity-100' : 'max-h-0 opacity-0 overflow-hidden'}`}>
        <div className="px-4 pb-4 border-t border-gray-100">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mt-4">
            {/* Search Input */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Tìm kiếm
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={searchParams.search || ''}
                  onChange={(e) => onSearch({ search: e.target.value })}
                  placeholder="Số thụ lý, bản án, tên..."
                  className="w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <div className="absolute left-2 top-2.5 text-gray-400">
                  🔍
                </div>
              </div>
            </div>

            {/* Loại án */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Loại án
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={searchParams.loaiAn || ''}
                  onChange={(e) => onSearch({ loaiAn: e.target.value })}
                  placeholder="Nhập loại án để tìm kiếm..."
                  className="w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <div className="absolute left-2 top-2.5 text-gray-400">
                  ⚖️
                </div>
              </div>
            </div>

            {/* Trạng thái */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Trạng thái
              </label>
              <div className="relative">
                <select
                  value={searchParams.trangThaiGiaiQuyet || ''}
                  onChange={(e) => onSearch({ trangThaiGiaiQuyet: e.target.value as any })}
                  className="w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent appearance-none bg-white"
                >
                  <option value="">Tất cả</option>
                  {TrangThaiGiaiQuyetEnum.options.map(option => (
                    <option key={option} value={option}>{option}</option>
                  ))}
                </select>
                <div className="absolute left-2 top-2.5 text-gray-400">
                  📊
                </div>
                <div className="absolute right-2 top-2.5 text-gray-400 pointer-events-none">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                  </svg>
                </div>
              </div>
            </div>

            {/* Thủ tục áp dụng */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Thủ tục áp dụng
              </label>
              <div className="relative">
                <input
                  type="text"
                  value={searchParams.thuTucApDung || ''}
                  onChange={(e) => onSearch({ thuTucApDung: e.target.value })}
                  placeholder="Nhập thủ tục áp dụng để tìm kiếm..."
                  className="w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <div className="absolute left-2 top-2.5 text-gray-400">
                  📝
                </div>
              </div>
            </div>

            {/* Từ ngày */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Từ ngày
              </label>
              <div className="relative">
                <input
                  type="date"
                  value={searchParams.fromDate || ''}
                  onChange={(e) => onSearch({ fromDate: e.target.value })}
                  className="w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <div className="absolute left-2 top-2.5 text-gray-400">
                  📅
                </div>
              </div>
            </div>

            {/* Đến ngày */}
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Đến ngày
              </label>
              <div className="relative">
                <input
                  type="date"
                  value={searchParams.toDate || ''}
                  onChange={(e) => onSearch({ toDate: e.target.value })}
                  className="w-full p-2 pl-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <div className="absolute left-2 top-2.5 text-gray-400">
                  📅
                </div>
              </div>
            </div>

            {/* Action buttons */}
            <div className="flex items-end gap-2">
              <button
                onClick={onReset}
                className="flex-1 px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-gray-500 transition-colors"
              >
                🗑️ Xóa bộ lọc
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CourtCaseFilter;
