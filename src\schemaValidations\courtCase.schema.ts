import { z } from 'zod';

// Enums for validation - keeping only TrangThaiGiaiQuyetEnum as enum
// LoaiAn and ThuTucApDung are now free text input fields

export const TrangThaiGiaiQuyetEnum = z.enum(['Chưa giải quyết', '<PERSON><PERSON> giải quyết', '<PERSON><PERSON> giải quyết']);

// Court Case Schema - All fields are optional for flexible data entry except STT which is auto-generated
export const CourtCaseSchema = z.object({
  // STT is auto-generated, so it's not included in input schema
  soVanThu: z.string().max(100).optional(), // Số văn thư
  ngayNhanVanThu: z.string().optional(), // Ngày nhận văn thư
  loaiAn: z.string().max(100).optional(),
  soThuLy: z.string().max(100).optional(),
  ngayThuLy: z.string().optional(),
  tand: z.string().max(200).optional(),

  // Thông tin bản án/quyết định
  soBanAn: z.string().max(100).optional(),
  ngayBanHanh: z.string().optional(),
  biCaoNguoiKhieuKien: z.string().max(500).optional(),
  toiDanhNoiDung: z.string().max(1000).optional(),

  quanHePhatLuat: z.string().max(500).optional(),
  hinhThucXuLy: z.string().max(200).optional(),
  thuTucApDung: z.string().max(100).optional(),
  thamPhanPhuTrach: z.string().max(200).optional(),
  truongPhoPhongKTNV: z.string().max(200).optional(), // Trưởng/Phó phòng KTNV/Thẩm tra viên

  // Thông tin bổ sung
  ghiChu: z.string().max(1000).optional(),
  ghiChuKetQua: z.string().max(1000).optional(), // Ghi chú KQ
  trangThaiGiaiQuyet: TrangThaiGiaiQuyetEnum.default('Chưa giải quyết'), // Trạng thái
  thoiHan90NgayVoiNgayNhanVanThu: z.string().optional(), // Thời hạn 90 ngày với ngày nhận văn thư
  thoiGianConLaiCuaThoiHan90NgayNhanVanThu: z.number().optional(), // Thời gian còn lại của thời hạn 90 ngày nhận văn thư
});

// Update schema (allows partial updates)
export const CourtCaseUpdateSchema = CourtCaseSchema.partial();

// Search/Filter schema
export const CourtCaseSearchSchema = z.object({
  page: z.number().int().positive().default(1),
  limit: z.number().int().positive().max(100).default(20),
  search: z.string().optional(),
  loaiAn: z.string().optional(),
  trangThaiGiaiQuyet: TrangThaiGiaiQuyetEnum.optional(),
  thuTucApDung: z.string().optional(),
  fromDate: z.string().optional(),
  toDate: z.string().optional(),
  sortBy: z.string().default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// Response types
export const CourtCaseItemSchema = CourtCaseSchema.extend({
  _id: z.string(),
  stt: z.number(), // Auto-generated and always present in responses
  createdAt: z.string(),
  updatedAt: z.string(),
  createdBy: z.object({
    _id: z.string(),
    username: z.string(),
    email: z.string().optional(),
  }).optional(),
  updatedBy: z.object({
    _id: z.string(),
    username: z.string(),
    email: z.string().optional(),
  }).optional(),
  // Formatted dates
  ngayNhanVanThuFormatted: z.string().optional(),
  ngayThuLyFormatted: z.string().optional(),
  ngayBanHanhFormatted: z.string().optional(),
  // Computed deadline fields
  thoiHan90Ngay: z.string().optional(),
  thoiHan90NgayFormatted: z.string().optional(),
  soNgayConLai: z.number().optional(),
  trangThaiThoiHan: z.enum(['Quá hạn', 'Sắp hết hạn', 'Gần hết hạn', 'Còn thời gian']).optional(),
});

export const CourtCaseListResponseSchema = z.object({
  success: z.boolean(),
  cases: z.array(CourtCaseItemSchema),
  pagination: z.object({
    currentPage: z.number(),
    totalPages: z.number(),
    totalItems: z.number(),
    itemsPerPage: z.number(),
    hasNextPage: z.boolean(),
    hasPrevPage: z.boolean(),
  }),
});

export const CourtCaseStatsSchema = z.object({
  success: z.boolean(),
  stats: z.object({
    total: z.number(),
    byStatus: z.array(z.object({
      _id: z.string(),
      count: z.number(),
    })),
    byType: z.array(z.object({
      _id: z.string(),
      count: z.number(),
    })),
    byProcedure: z.array(z.object({
      _id: z.string(),
      count: z.number(),
    })),
  }),
});

// Type exports
export type CourtCaseType = z.infer<typeof CourtCaseSchema>;
export type CourtCaseUpdateType = z.infer<typeof CourtCaseUpdateSchema>;
export type CourtCaseItemType = z.infer<typeof CourtCaseItemSchema>;
export type CourtCaseSearchType = z.infer<typeof CourtCaseSearchSchema>;
export type CourtCaseListResponseType = z.infer<typeof CourtCaseListResponseSchema>;
export type CourtCaseStatsType = z.infer<typeof CourtCaseStatsSchema>;

// Enum type exports
export type TrangThaiGiaiQuyetType = z.infer<typeof TrangThaiGiaiQuyetEnum>;
