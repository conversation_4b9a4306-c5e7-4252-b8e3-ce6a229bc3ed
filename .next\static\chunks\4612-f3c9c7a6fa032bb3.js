(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[4612],{174:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var o=r(2115),n=r(8637),a=r.n(n);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e}).apply(this,arguments)}var s=(0,o.forwardRef)(function(e,t){var r=e.color,n=e.size,a=void 0===n?24:n,s=function(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r,o,n={},a=Object.keys(e);for(o=0;o<a.length;o++)r=a[o],t.indexOf(r)>=0||(n[r]=e[r]);return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)r=a[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,["color","size"]);return o.createElement("svg",i({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s),o.createElement("line",{x1:"12",y1:"2",x2:"12",y2:"6"}),o.createElement("line",{x1:"12",y1:"18",x2:"12",y2:"22"}),o.createElement("line",{x1:"4.93",y1:"4.93",x2:"7.76",y2:"7.76"}),o.createElement("line",{x1:"16.24",y1:"16.24",x2:"19.07",y2:"19.07"}),o.createElement("line",{x1:"2",y1:"12",x2:"6",y2:"12"}),o.createElement("line",{x1:"18",y1:"12",x2:"22",y2:"12"}),o.createElement("line",{x1:"4.93",y1:"19.07",x2:"7.76",y2:"16.24"}),o.createElement("line",{x1:"16.24",y1:"7.76",x2:"19.07",y2:"4.93"}))});s.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},s.displayName="Loader";let l=s},1612:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>c});var o={};r.r(o),r.d(o,{BRAND:()=>s.qt,DIRTY:()=>a.jm,EMPTY_PATH:()=>a.I3,INVALID:()=>a.uY,NEVER:()=>s.tm,OK:()=>a.OK,ParseStatus:()=>a.MY,Schema:()=>s.Sj,ZodAny:()=>s.Ml,ZodArray:()=>s.n,ZodBigInt:()=>s.Lr,ZodBoolean:()=>s.WF,ZodBranded:()=>s.eN,ZodCatch:()=>s.hw,ZodDate:()=>s.aP,ZodDefault:()=>s.Xi,ZodDiscriminatedUnion:()=>s.jv,ZodEffects:()=>s.k1,ZodEnum:()=>s.Vb,ZodError:()=>l.G,ZodFirstPartyTypeKind:()=>s.kY,ZodFunction:()=>s.CZ,ZodIntersection:()=>s.Jv,ZodIssueCode:()=>l.eq,ZodLazy:()=>s.Ih,ZodLiteral:()=>s.DN,ZodMap:()=>s.Ut,ZodNaN:()=>s.Tq,ZodNativeEnum:()=>s.WM,ZodNever:()=>s.iS,ZodNull:()=>s.PQ,ZodNullable:()=>s.l1,ZodNumber:()=>s.rS,ZodObject:()=>s.bv,ZodOptional:()=>s.Ii,ZodParsedType:()=>i.Zp,ZodPipeline:()=>s._c,ZodPromise:()=>s.$i,ZodReadonly:()=>s.EV,ZodRecord:()=>s.b8,ZodSchema:()=>s.lK,ZodSet:()=>s.Kz,ZodString:()=>s.ND,ZodSymbol:()=>s.K5,ZodTransformer:()=>s.BG,ZodTuple:()=>s.y0,ZodType:()=>s.aR,ZodUndefined:()=>s._Z,ZodUnion:()=>s.fZ,ZodUnknown:()=>s._,ZodVoid:()=>s.a0,addIssueToContext:()=>a.zn,any:()=>s.bz,array:()=>s.YO,bigint:()=>s.o,boolean:()=>s.zM,coerce:()=>s.au,custom:()=>s.Ie,date:()=>s.p6,datetimeRegex:()=>s.fm,defaultErrorMap:()=>n.su,discriminatedUnion:()=>s.gM,effect:()=>s.QZ,enum:()=>s.k5,function:()=>s.fH,getErrorMap:()=>n.$W,getParsedType:()=>i.CR,instanceof:()=>s.Nl,intersection:()=>s.E$,isAborted:()=>a.G4,isAsync:()=>a.xP,isDirty:()=>a.DM,isValid:()=>a.fn,late:()=>s.fn,lazy:()=>s.RZ,literal:()=>s.eu,makeIssue:()=>a.y7,map:()=>s.Tj,nan:()=>s.oi,nativeEnum:()=>s.fc,never:()=>s.Zm,null:()=>s.ch,nullable:()=>s.me,number:()=>s.ai,object:()=>s.Ik,objectUtil:()=>i.o6,oboolean:()=>s.yN,onumber:()=>s.p7,optional:()=>s.lq,ostring:()=>s.Di,pipeline:()=>s.Tk,preprocess:()=>s.vk,promise:()=>s.iv,quotelessJson:()=>l.WI,record:()=>s.g1,set:()=>s.hZ,setErrorMap:()=>n.pJ,strictObject:()=>s.re,string:()=>s.Yj,symbol:()=>s.HR,transformer:()=>s.Gu,tuple:()=>s.PV,undefined:()=>s.Vx,union:()=>s.KC,unknown:()=>s.L5,util:()=>i.ZS,void:()=>s.rI});var n=r(5722),a=r(3454),i=r(6227),s=r(4556),l=r(4028);let c=o},2948:e=>{"use strict";e.exports="SECRET_DO_NOT_PASS_THIS_OR_YOU_WILL_BE_FIRED"},3888:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var o=r(5155),n=r(2115),a=r(4272),i=r(7691),s=r(4559);let l=e=>{let{serverImageUrl:t,onUploadFeatureImg:r,onDeleteFeatureImg:l}=e,[c,d]=(0,n.useState)(""),[u,p]=(0,n.useState)(""),[m,y]=(0,n.useState)(!1),[f,h]=(0,n.useState)(t||"");return(0,n.useEffect)(()=>{t&&h(function(e){if(!e)return"";let t=(e.startsWith("/")?e.slice(1):e).replace("server/uploads/","uploads/");return"".concat(s.A.NEXT_PUBLIC_API_ENDPOINT,"/").concat(t)}(t))},[t]),(0,o.jsxs)("div",{className:"w-full block mb-2",children:[(0,o.jsxs)("label",{className:"flex flex-col px-4 py-6 border-dashed text-center border border-gray-400 cursor-pointer",children:[(0,o.jsx)("input",{className:"hidden",type:"file",accept:"image/png, image/jpeg, image/bmp, image/gif",onChange:e=>{var t;let o=null==(t=e.target.files)?void 0:t[0];if(!o)return;let n=o.size/4024/4024;if(!o.type.match("image.*"))return void d("Please choose an image file");if(y(!0),d(""),n>1)return void d("Your file is too big! Please select an image under 1MB");let a=URL.createObjectURL(o);p(o.name),h(a),d(""),r(o)}}),(0,o.jsxs)("span",{className:"text-center block",children:[(0,o.jsx)("span",{className:"flex justify-center",children:(0,o.jsx)(a.A,{})}),(0,o.jsx)("span",{className:"file-label",children:"Tải ảnh l\xean..."})]}),u&&(0,o.jsxs)("span",{className:"mt-2 text-sm",children:["Đ\xe3 chọn: ",u]}),c&&(0,o.jsx)("span",{className:"text-red-500 mt-2",children:c})]}),f&&(0,o.jsxs)("div",{className:"relative",children:[(0,o.jsx)("div",{className:"mt-4",children:(0,o.jsx)("img",{src:f,alt:"Preview",className:"max-w-full h-auto rounded"})}),(0,o.jsx)("button",{type:"button",onClick:()=>{p(""),h(""),d(""),l()},className:"mt-4 text-sm text-red-600 hover:underline absolute right-2 top-2",children:(0,o.jsx)(i.A,{})})]})]})}},3931:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var o=r(7937);let n={fetchSetting:e=>o.Ay.get("api/setting/admin",{headers:{Authorization:"Bearer ".concat(e)}}),commonFetchSetting:()=>o.Ay.get("api/setting/"),CraeteSetting:(e,t)=>o.Ay.put("api/setting/",e,{headers:{Authorization:"Bearer ".concat(t)}}),EditorSetting:(e,t)=>o.Ay.put("api/setting/editor",e,{headers:{Authorization:"Bearer ".concat(t)}}),CraeteMenu:(e,t)=>o.Ay.post("api/menu/",e,{headers:{Authorization:"Bearer ".concat(t)}}),EditMenu:(e,t)=>o.Ay.put("api/menu/edit",e,{headers:{Authorization:"Bearer ".concat(t)}}),GetMenu:e=>o.Ay.get("api/menu/".concat(e)),fetchMenus:(e,t)=>o.Ay.post("api/menu/get-all",e,{headers:{Authorization:"Bearer ".concat(t)}}),deleteMenu:(e,t)=>o.Ay.delete("api/menu/".concat(e._id),{headers:{Authorization:"Bearer ".concat(t)}})}},4272:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var o=r(2115),n=r(8637),a=r.n(n);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e}).apply(this,arguments)}var s=(0,o.forwardRef)(function(e,t){var r=e.color,n=e.size,a=void 0===n?24:n,s=function(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r,o,n={},a=Object.keys(e);for(o=0;o<a.length;o++)r=a[o],t.indexOf(r)>=0||(n[r]=e[r]);return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)r=a[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,["color","size"]);return o.createElement("svg",i({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s),o.createElement("path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"}),o.createElement("polyline",{points:"17 8 12 3 7 8"}),o.createElement("line",{x1:"12",y1:"3",x2:"12",y2:"15"}))});s.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},s.displayName="Upload";let l=s},4559:(e,t,r)=>{"use strict";r.d(t,{A:()=>i});var o=r(4556),n=r(9509);let a=o.Ik({NEXT_PUBLIC_API_ENDPOINT:o.Yj().url(),NEXT_PUBLIC_URL:o.Yj().url(),CRYPTOJS_SECRECT:o.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:n.env.CRYPTOJS_SECRECT});if(!a.success)throw console.error("Invalid environment variables:",a.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let i=a.data},5937:(e,t,r)=>{"use strict";r.d(t,{lV:()=>u,MJ:()=>v,zB:()=>m,eI:()=>h,lR:()=>g,C5:()=>b});var o=r(5155),n=r(2115),a=r(4624),i=r(2177),s=r(9434),l=r(7073);let c=(0,r(2085).F)("text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 mb-2"),d=n.forwardRef((e,t)=>{let{className:r,...n}=e;return(0,o.jsx)(l.b,{ref:t,className:(0,s.cn)(c(),r),...n})});d.displayName=l.b.displayName;let u=i.Op,p=n.createContext({}),m=e=>{let{...t}=e;return(0,o.jsx)(p.Provider,{value:{name:t.name},children:(0,o.jsx)(i.xI,{...t})})},y=()=>{let e=n.useContext(p),t=n.useContext(f),{getFieldState:r,formState:o}=(0,i.xW)(),a=r(e.name,o);if(!e)throw Error("useFormField should be used within <FormField>");let{id:s}=t;return{id:s,name:e.name,formItemId:"".concat(s,"-form-item"),formDescriptionId:"".concat(s,"-form-item-description"),formMessageId:"".concat(s,"-form-item-message"),...a}},f=n.createContext({}),h=n.forwardRef((e,t)=>{let{className:r,...a}=e,i=n.useId();return(0,o.jsx)(f.Provider,{value:{id:i},children:(0,o.jsx)("div",{ref:t,className:(0,s.cn)("mb-4",r),...a})})});h.displayName="FormItem";let g=n.forwardRef((e,t)=>{let{className:r,...n}=e,{error:a,formItemId:i}=y();return(0,o.jsx)(d,{ref:t,className:(0,s.cn)(a&&"text-destructive",r),htmlFor:i,...n})});g.displayName="FormLabel";let v=n.forwardRef((e,t)=>{let{...r}=e,{error:n,formItemId:i,formDescriptionId:s,formMessageId:l}=y();return(0,o.jsx)(a.DX,{ref:t,id:i,"aria-describedby":n?"".concat(s," ").concat(l):"".concat(s),"aria-invalid":!!n,...r})});v.displayName="FormControl",n.forwardRef((e,t)=>{let{className:r,...n}=e,{formDescriptionId:a}=y();return(0,o.jsx)("p",{ref:t,id:a,className:(0,s.cn)("text-[0.8rem] text-muted-foreground",r),...n})}).displayName="FormDescription";let b=n.forwardRef((e,t)=>{let{className:r,children:n,...a}=e,{error:i,formMessageId:l}=y(),c=i?String(null==i?void 0:i.message):n;return c?(0,o.jsx)("p",{ref:t,id:l,className:(0,s.cn)("text-[0.8rem] font-medium text-red-600",r),...a,children:c}):null});b.displayName="FormMessage"},7073:(e,t,r)=>{"use strict";r.d(t,{b:()=>l});var o=r(2115);r(7650);var n=r(4624),a=r(5155),i=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let r=(0,n.TL)(`Primitive.${t}`),i=o.forwardRef((e,o)=>{let{asChild:n,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(n?r:t,{...i,ref:o})});return i.displayName=`Primitive.${t}`,{...e,[t]:i}},{}),s=o.forwardRef((e,t)=>(0,a.jsx)(i.label,{...e,ref:t,onMouseDown:t=>{var r;t.target.closest("button, input, select, textarea")||(null==(r=e.onMouseDown)||r.call(e,t),!t.defaultPrevented&&t.detail>1&&t.preventDefault())}}));s.displayName="Label";var l=s},7691:(e,t,r)=>{"use strict";r.d(t,{A:()=>l});var o=r(2115),n=r(8637),a=r.n(n);function i(){return(i=Object.assign||function(e){for(var t=1;t<arguments.length;t++){var r=arguments[t];for(var o in r)Object.prototype.hasOwnProperty.call(r,o)&&(e[o]=r[o])}return e}).apply(this,arguments)}var s=(0,o.forwardRef)(function(e,t){var r=e.color,n=e.size,a=void 0===n?24:n,s=function(e,t){if(null==e)return{};var r,o,n=function(e,t){if(null==e)return{};var r,o,n={},a=Object.keys(e);for(o=0;o<a.length;o++)r=a[o],t.indexOf(r)>=0||(n[r]=e[r]);return n}(e,t);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(e);for(o=0;o<a.length;o++)r=a[o],!(t.indexOf(r)>=0)&&Object.prototype.propertyIsEnumerable.call(e,r)&&(n[r]=e[r])}return n}(e,["color","size"]);return o.createElement("svg",i({ref:t,xmlns:"http://www.w3.org/2000/svg",width:a,height:a,viewBox:"0 0 24 24",fill:"none",stroke:void 0===r?"currentColor":r,strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"},s),o.createElement("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),o.createElement("line",{x1:"6",y1:"6",x2:"18",y2:"18"}))});s.propTypes={color:a().string,size:a().oneOfType([a().string,a().number])},s.displayName="X";let l=s},7937:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>d});var o=r(4559),n=r(9434),a=r(5695);class i extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class s extends i{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let l=null,c=async(e,t,r)=>{let c;(null==r?void 0:r.body)instanceof FormData?c=r.body:(null==r?void 0:r.body)&&(c=JSON.stringify(r.body));let d=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(d.Authorization="Bearer ".concat(e))}let u=(null==r?void 0:r.baseUrl)===void 0?o.A.NEXT_PUBLIC_API_ENDPOINT:r.baseUrl,p=t.startsWith("/")?"".concat(u).concat(t):"".concat(u,"/").concat(t),m=await fetch(p,{...r,headers:{...d,...null==r?void 0:r.headers},body:c,method:e}),y=null,f=m.headers.get("content-type");if(f&&f.includes("application/json"))try{y=await m.json()}catch(e){console.error("Failed to parse JSON response:",e),y=null}else y=await m.text();let h={status:m.status,payload:y};if(!m.ok)if(404===m.status||403===m.status)throw new s(h);else if(401===m.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,a.redirect)("/logout?sessionToken=".concat(e))}else if(!l){l=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...d}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await l}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),l=null,location.href="/login"}}}else throw new i(h);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,n.Fd)(t))){let{token:e}=y;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,n.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return h},d={get:(e,t)=>c("GET",e,t),post:(e,t,r)=>c("POST",e,{...r,body:t}),put:(e,t,r)=>c("PUT",e,{...r,body:t}),patch:(e,t,r)=>c("PATCH",e,{...r,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},8637:(e,t,r)=>{e.exports=r(9399)()},8819:(e,t,r)=>{"use strict";r.d(t,{ri:()=>n});var o=r(1612);o.Ay.object({message:o.Ay.string()}).strict();let n=o.Ay.object({title:o.Ay.string().min(1,"Title is required"),desc:o.Ay.string().optional(),address:o.Ay.string().optional(),email:o.Ay.string().optional(),hotline:o.Ay.string().optional(),contact:o.Ay.string().optional(),copyright:o.Ay.string().optional(),footerBLock1:o.Ay.string().optional(),footerBLock2:o.Ay.string().optional(),logo:o.Ay.object({_id:o.Ay.string(),path:o.Ay.string(),folder:o.Ay.string()}),ads1:o.Ay.string().optional(),openReg:o.Ay.boolean().optional()});o.Ay.object({_id:o.Ay.string().optional(),name:o.Ay.string().min(1,"Name is required"),slug:o.Ay.string().min(1,"Slug is required"),tasks:o.Ay.array(o.Ay.any()).optional(),id:o.Ay.number(),droppable:o.Ay.boolean(),parent:o.Ay.number(),text:o.Ay.string().min(1,"Name is required")}),o.Ay.object({_id:o.Ay.string().optional(),title:o.Ay.string(),position:o.Ay.number()})},8890:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var o=r(7937);let n={postMedia:(e,t)=>o.Ay.post("api/media/product",e,{headers:{Authorization:"Bearer ".concat(t)}}),postLogo:(e,t)=>o.Ay.post("api/media/single-noresize",e,{headers:{Authorization:"Bearer ".concat(t)}}),postFileMedia:(e,t)=>o.Ay.post("api/media/file",e,{headers:{Authorization:"Bearer ".concat(t)}}),postVideo:(e,t)=>o.Ay.post("api/video/upload",e,{headers:{Authorization:"Bearer ".concat(t)}})}},9399:(e,t,r)=>{"use strict";var o=r(2948);function n(){}function a(){}a.resetWarningCache=n,e.exports=function(){function e(e,t,r,n,a,i){if(i!==o){var s=Error("Calling PropTypes validators directly is not supported by the `prop-types` package. Use PropTypes.checkPropTypes() to call them. Read more at http://fb.me/use-check-prop-types");throw s.name="Invariant Violation",s}}function t(){return e}e.isRequired=e;var r={array:e,bigint:e,bool:e,func:e,number:e,object:e,string:e,symbol:e,any:e,arrayOf:t,element:e,elementType:e,instanceOf:t,node:e,objectOf:t,oneOf:t,oneOfType:t,shape:t,exact:t,checkPropTypes:a,resetWarningCache:n};return r.PropTypes=r,r}},9434:(e,t,r)=>{"use strict";r.d(t,{Fd:()=>i,cn:()=>a}),r(7937);var o=r(2596),n=r(9688);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,n.QP)((0,o.$)(t))}r(8801);let i=e=>e.startsWith("/")?e.slice(1):e}}]);