(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[2020],{1592:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(7937);r(7967).config();let a={login:e=>o.Ay.post("/api/auth/login",e),register:e=>o.Ay.post("/api/auth/signup",e),forgot:e=>o.Ay.put("/api/auth/app-forgot-pass",e),changepass:e=>o.Ay.put("/api/auth/app-reset-pass",e),auth:e=>o.Ay.post("/api/auth",e,{baseUrl:""}),checkCode:e=>o.Ay.get("/api/auth/check-code/".concat(e)),VerifyAppCode:e=>o.Ay.post("/api/auth/verify-app-code",e),VerifyCode:e=>o.Ay.post("/api/auth/verify-code",e),logoutFromNextServerToServer:e=>o.Ay.post("/api/auth/blacklist-token/",e,{headers:{Authorization:"Bearer ".concat(e.sessionToken)}}),logoutFromNextClientToNextServer:(e,t)=>o.Ay.post("/api/auth/logout",{force:e},{baseUrl:"",signal:t}),slideSessionFromNextServerToServer:e=>o.Ay.post("/auth/slide-session",{},{headers:{Authorization:"Bearer ".concat(e)}}),slideSessionFromNextClientToNextServer:()=>o.Ay.post("/api/auth/slide-session",{},{baseUrl:""})}},2016:()=>{},2445:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var o=r(3348);let a={admin:["admin","editor","manager","user"],editor:["editor","user"],manager:["manager","editor","user"],user:["user"]},s=e=>["admin","editor","manager","user"].includes(e),n=()=>{let{user:e}=(0,o.U)();return{user:e,isAuthenticated:!!e,hasPermission:t=>!!(e&&s(e.rule))&&((e,t)=>{var r,o;return!!s(e)&&null!=(o=null==(r=a[e])?void 0:r.includes(t))&&o})(e.rule,t)}}},3348:(e,t,r)=>{"use strict";r.d(t,{U:()=>n,default:()=>i});var o=r(5155),a=r(2115);let s=(0,a.createContext)({user:null,setUser:()=>{},isAuthenticated:!1,isLoading:!0}),n=()=>(0,a.useContext)(s),i=e=>{let{children:t}=e,[r,n]=(0,a.useState)(()=>null),[i,l]=(0,a.useState)(!0),c=(0,a.useCallback)(e=>{n(e),localStorage.setItem("user",JSON.stringify(e))},[n]);return(0,a.useEffect)(()=>{let e=localStorage.getItem("user");n(e?JSON.parse(e):null),l(!1)},[n]),(0,o.jsx)(s.Provider,{value:{user:r,setUser:c,isAuthenticated:!!r,isLoading:i},children:t})}},3859:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var o=r(5155),a=r(6874),s=r.n(a),n=r(3348),i=r(2445),l=r(5404);function c(){var e;let{user:t,isAuthenticated:r}=(0,n.U)(),{hasPermission:a}=(0,i.A)();return(0,o.jsx)("div",{className:"flex-shrink-0 flex items-center justify-end",children:(0,o.jsxs)("div",{className:"dropdown dropdown-end",children:[(0,o.jsx)("div",{className:"tooltip tooltip-left","data-tip":null==t?void 0:t.username,children:(0,o.jsx)("label",{tabIndex:0,className:"cursor-pointer flex w-8 h-8 md:w-10 md:h-10 bg-yellow-400 rounded-full text-center text-gray-900 items-center justify-center text-xl mb-0 uppercase",children:null==t||null==(e=t.username)?void 0:e.charAt(0)})}),(0,o.jsxs)("ul",{tabIndex:0,className:"dropdown-content menu bg-base-100 rounded-box z-[1] w-52 p-2 shadow",children:[[{title:"T\xe0i khoản",to:"/dashboard"}].map((e,t)=>(0,o.jsx)("li",{children:(0,o.jsx)(s(),{href:e.to,children:e.title})},t)),(0,o.jsx)("li",{children:(0,o.jsx)(l.A,{})})]})]})})}},3931:(e,t,r)=>{"use strict";r.d(t,{A:()=>a});var o=r(7937);let a={fetchSetting:e=>o.Ay.get("api/setting/admin",{headers:{Authorization:"Bearer ".concat(e)}}),commonFetchSetting:()=>o.Ay.get("api/setting/"),CraeteSetting:(e,t)=>o.Ay.put("api/setting/",e,{headers:{Authorization:"Bearer ".concat(t)}}),EditorSetting:(e,t)=>o.Ay.put("api/setting/editor",e,{headers:{Authorization:"Bearer ".concat(t)}}),CraeteMenu:(e,t)=>o.Ay.post("api/menu/",e,{headers:{Authorization:"Bearer ".concat(t)}}),EditMenu:(e,t)=>o.Ay.put("api/menu/edit",e,{headers:{Authorization:"Bearer ".concat(t)}}),GetMenu:e=>o.Ay.get("api/menu/".concat(e)),fetchMenus:(e,t)=>o.Ay.post("api/menu/get-all",e,{headers:{Authorization:"Bearer ".concat(t)}}),deleteMenu:(e,t)=>o.Ay.delete("api/menu/".concat(e._id),{headers:{Authorization:"Bearer ".concat(t)}})}},4559:(e,t,r)=>{"use strict";r.d(t,{A:()=>n});var o=r(4556),a=r(9509);let s=o.Ik({NEXT_PUBLIC_API_ENDPOINT:o.Yj().url(),NEXT_PUBLIC_URL:o.Yj().url(),CRYPTOJS_SECRECT:o.bz()}).safeParse({NEXT_PUBLIC_API_ENDPOINT:"http://localhost:3000",NEXT_PUBLIC_URL:"http://localhost:3000",CRYPTOJS_SECRECT:a.env.CRYPTOJS_SECRECT});if(!s.success)throw console.error("Invalid environment variables:",s.error.issues),Error("C\xe1c gi\xe1 trị khai b\xe1o trong file .env kh\xf4ng hợp lệ");let n=s.data},5404:(e,t,r)=>{"use strict";r.d(t,{A:()=>c});var o=r(5155),a=r(1592),s=r(3348),n=r(5695),i=r(8543),l=r(6312);function c(e){let{compact:t=!1}=e,{setUser:r}=(0,s.U)();(0,n.useRouter)(),(0,n.usePathname)();let c=async()=>{try{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),localStorage.removeItem("sessionTokenExpiresAt"),await a.A.logoutFromNextClientToNextServer(),r(null),i.oR.success("Đăng xuất th\xe0nh c\xf4ng!"),window.location.href="/login?logout=true"}catch(e){console.error("Logout error:",e);try{await a.A.logoutFromNextClientToNextServer(!0)}catch(e){console.error("Force logout error:",e)}localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),localStorage.removeItem("sessionTokenExpiresAt"),r(null),window.location.href="/login?force=true"}};return(0,o.jsxs)("button",{className:"\n        flex items-center justify-center gap-2 px-4 py-2 \n        bg-red-500 hover:bg-red-600 text-white text-sm font-medium \n        rounded-lg transition-colors duration-200\n        ".concat(t?"w-10 h-10 p-0":"w-full","\n      "),onClick:c,title:t?"Đăng xuất":"",children:[(0,o.jsx)(l.A,{size:18}),!t&&(0,o.jsx)("span",{children:"Đăng xuất"})]})}},7937:(e,t,r)=>{"use strict";r.d(t,{Ay:()=>u});var o=r(4559),a=r(9434),s=r(5695);class n extends Error{constructor({status:e,payload:t}){super("Http Error"),this.status=e,this.payload=t}}class i extends n{constructor({status:e,payload:t}){super({status:e,payload:t}),this.status=e,this.payload=t}}let l=null,c=async(e,t,r)=>{let c;(null==r?void 0:r.body)instanceof FormData?c=r.body:(null==r?void 0:r.body)&&(c=JSON.stringify(r.body));let u=c instanceof FormData?{}:{"Content-Type":"application/json"};{let e=localStorage.getItem("sessionToken");e&&(u.Authorization="Bearer ".concat(e))}let d=(null==r?void 0:r.baseUrl)===void 0?o.A.NEXT_PUBLIC_API_ENDPOINT:r.baseUrl,h=t.startsWith("/")?"".concat(d).concat(t):"".concat(d,"/").concat(t),g=await fetch(h,{...r,headers:{...u,...null==r?void 0:r.headers},body:c,method:e}),p=null,m=g.headers.get("content-type");if(m&&m.includes("application/json"))try{p=await g.json()}catch(e){console.error("Failed to parse JSON response:",e),p=null}else p=await g.text();let f={status:g.status,payload:p};if(!g.ok)if(404===g.status||403===g.status)throw new i(f);else if(401===g.status){if(0){let e="";e=localStorage.getItem("sessionToken")||"",(0,s.redirect)("/logout?sessionToken=".concat(e))}else if(!l){l=fetch("/api/auth/logout",{method:"POST",body:JSON.stringify({force:!0}),headers:{...u}});try{let e=async e=>{if(e.origin!=="".concat("http://localhost:3000"))return};window.addEventListener("message",e),await l}catch(e){}finally{localStorage.removeItem("user"),localStorage.removeItem("sessionToken"),l=null,location.href="/login"}}}else throw new n(f);if(["api/auth/verify-app-code","api/auth/verify-code","api/auth/login","auth"].some(e=>e===(0,a.Fd)(t))){let{token:e}=p;localStorage.setItem("sessionToken",e)}else"auth/logout"===(0,a.Fd)(t)&&(localStorage.removeItem("user"),localStorage.removeItem("sessionToken"));return f},u={get:(e,t)=>c("GET",e,t),post:(e,t,r)=>c("POST",e,{...r,body:t}),put:(e,t,r)=>c("PUT",e,{...r,body:t}),patch:(e,t,r)=>c("PATCH",e,{...r,body:t}),delete:(e,t)=>c("DELETE",e,{...t})}},9434:(e,t,r)=>{"use strict";r.d(t,{Fd:()=>n,cn:()=>s}),r(7937);var o=r(2596),a=r(9688);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.QP)((0,o.$)(t))}r(8801);let n=e=>e.startsWith("/")?e.slice(1):e},9843:(e,t,r)=>{"use strict";r.d(t,{SettingProvider:()=>d,i:()=>h});var o=r(5155),a=r(2115),s=r(3931),n=r(5695);let i=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30;try{let r=localStorage.getItem("".concat(e,"_timestamp"));if(!r)return!0;let o=parseInt(r);return Date.now()-o>60*t*1e3}catch(e){return console.error("❌ Error checking cache staleness:",e),!0}},l=(e,t)=>{try{localStorage.setItem(e,JSON.stringify(t)),localStorage.setItem("".concat(e,"_timestamp"),Date.now().toString())}catch(e){console.error("❌ Error setting cache with timestamp:",e)}},c=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:30;try{if(i(e,t))return null;let r=localStorage.getItem(e);return r?JSON.parse(r):null}catch(e){return console.error("❌ Error getting fresh cache:",e),null}},u=(0,a.createContext)(void 0),d=e=>{let{children:t}=e,[r,i]=(0,a.useState)(null),[d,h]=(0,a.useState)(null),[g,p]=(0,a.useState)(!0),m=(0,n.usePathname)(),f=async function(){let e=arguments.length>0&&void 0!==arguments[0]&&arguments[0];try{if(p(!0),!e){let e=c("siteSetting",5),t=c("siteMenus",5);if(e&&t){i(e),h(t),p(!1);return}}let t=await s.A.commonFetchSetting();t.payload.success?(i(t.payload.setting),h(t.payload.menus),l("siteSetting",t.payload.setting),l("siteMenus",t.payload.menus)):console.error("Failed to fetch settings")}catch(e){console.error("Error fetching settings:",e)}finally{p(!1)}};return(0,a.useEffect)(()=>{if("/"===m||m.startsWith("/dashboard"))f();else{let e=c("siteSetting",30),t=c("siteMenus",30);e&&t?(i(e),h(t),p(!1)):f()}},[m]),(0,o.jsx)(u.Provider,{value:{setting:r,loading:g,menus:d,refreshSettings:()=>{console.log("\uD83D\uDD04 Force refreshing settings...");try{localStorage.removeItem("siteSetting"),localStorage.removeItem("siteMenus"),console.log("✅ Setting cache cleared")}catch(e){console.error("❌ Error clearing setting cache:",e)}f(!0)}},children:t})},h=()=>{let e=(0,a.useContext)(u);if(!e)throw Error("useSetting must be used within a SettingProvider");return e}}}]);